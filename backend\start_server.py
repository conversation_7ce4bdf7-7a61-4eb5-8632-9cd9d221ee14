#!/usr/bin/env python3
"""
Startup script for LeetCode Helper Pro AI Backend
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")

def check_requirements():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import google.generativeai
        print("✅ Core packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def setup_environment():
    """Setup environment variables"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Creating .env file from .env.example")
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("⚠️  Please edit .env file with your API keys before starting the server")
            return False
        else:
            print("❌ No .env.example file found")
            return False
    
    # Check for required API keys
    from dotenv import load_dotenv
    load_dotenv()
    
    missing_keys = []
    if not os.getenv('GOOGLE_API_KEY'):
        missing_keys.append('GOOGLE_API_KEY')
    if not os.getenv('YOUTUBE_API_KEY'):
        missing_keys.append('YOUTUBE_API_KEY')
    
    if missing_keys:
        print(f"⚠️  Missing API keys in .env: {', '.join(missing_keys)}")
        print("Some features will be disabled without these keys")
    else:
        print("✅ API keys configured")
    
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements")
        return False

def start_server(host="localhost", port=8000, reload=True):
    """Start the FastAPI server"""
    print(f"🚀 Starting LeetCode Helper Pro AI Backend on {host}:{port}")
    print(f"📖 API Documentation: http://{host}:{port}/docs")
    print("Press Ctrl+C to stop the server")
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    parser = argparse.ArgumentParser(description="LeetCode Helper Pro AI Backend")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--install", action="store_true", help="Install requirements and exit")
    parser.add_argument("--check", action="store_true", help="Check setup and exit")
    
    args = parser.parse_args()
    
    print("🧠 LeetCode Helper Pro AI Backend")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    
    # Install requirements if requested
    if args.install:
        if install_requirements():
            print("✅ Installation complete")
        else:
            sys.exit(1)
        return
    
    # Check requirements
    if not check_requirements():
        print("\n💡 Run with --install to install requirements")
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Check setup if requested
    if args.check:
        print("✅ Setup check complete")
        return
    
    # Start server
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload
    )

if __name__ == "__main__":
    main()
