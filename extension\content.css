/* Content Script Styles for LeetCode Helper Pro */

/* Floating Helper <PERSON> */
#leetcode-helper-float {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 10000;
    opacity: 0.8;
    transition: all 0.3s ease;
}

#leetcode-helper-float:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.05);
}

#leetcode-helper-float.active {
    opacity: 1;
}

.helper-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 25px;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.helper-btn:hover {
    box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

.helper-icon {
    font-size: 18px;
}

.helper-text {
    white-space: nowrap;
}

/* Quick Hints Overlay */
#leetcode-helper-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 20000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.quick-hints-panel {
    background: #1a1a2e;
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideUp 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-btn {
    background: none;
    border: none;
    color: #8892b0;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: white;
}

.panel-body {
    padding: 24px;
}

.hint-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.quick-hint-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 18px;
    background: #16213e;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.quick-hint-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.quick-hint-btn:hover::before {
    left: 100%;
}

.quick-hint-btn:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.quick-hint-btn.gentle:hover {
    border-color: #4facfe;
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
}

.quick-hint-btn.moderate:hover {
    border-color: #43e97b;
    box-shadow: 0 4px 16px rgba(67, 233, 123, 0.3);
}

.quick-hint-btn.strong:hover {
    border-color: #fa709a;
    box-shadow: 0 4px 16px rgba(250, 112, 154, 0.3);
}

.hint-display {
    background: #0f3460;
    border-radius: 10px;
    padding: 20px;
    min-height: 100px;
    color: #b8c5d6;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.hint-content {
    animation: fadeInContent 0.3s ease;
}

@keyframes fadeInContent {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hint-content.gentle {
    border-left: 3px solid #4facfe;
    padding-left: 16px;
}

.hint-content.moderate {
    border-left: 3px solid #43e97b;
    padding-left: 16px;
}

.hint-content.strong {
    border-left: 3px solid #fa709a;
    padding-left: 16px;
}

.hint-content strong {
    color: white;
    display: block;
    margin-bottom: 8px;
}

/* Context Menu Styles */
.leetcode-helper-context {
    background: #1a1a2e;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index: 15000;
}

.context-menu-item {
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.context-menu-item:hover {
    background: #16213e;
}

.context-menu-item .icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    #leetcode-helper-float {
        right: 10px;
    }
    
    .helper-btn {
        padding: 10px 12px;
        font-size: 12px;
    }
    
    .helper-text {
        display: none;
    }
    
    .quick-hints-panel {
        width: 95%;
        margin: 10px;
    }
    
    .panel-body {
        padding: 16px;
    }
    
    .hint-buttons {
        gap: 8px;
    }
    
    .quick-hint-btn {
        padding: 12px 14px;
        font-size: 13px;
    }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
    .quick-hints-panel {
        background: #1a1a2e;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .helper-btn {
        border: 2px solid white;
    }
    
    .quick-hints-panel {
        border: 2px solid white;
    }
    
    .quick-hint-btn {
        border: 1px solid white;
    }
}

/* Inline Hint Styles */
.leetcode-helper-inline-hint {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    margin: 16px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    animation: slideInFromTop 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index: 9999;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.inline-hint-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.hint-level {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 12px;
    border-radius: 20px;
    text-transform: uppercase;
}

.hint-level.gentle {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.hint-level.moderate {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.hint-level.strong {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.inline-hint-close {
    background: none;
    border: none;
    color: #8892b0;
    font-size: 20px;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

.inline-hint-close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.inline-hint-content {
    padding: 20px;
    color: #b8c5d6;
    line-height: 1.6;
    font-size: 14px;
}

/* Modal Styles */
.leetcode-helper-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 20000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
}

.modal-container {
    background: #1a1a2e;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    background: none;
    border: none;
    color: #8892b0;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

.modal-close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
    color: #b8c5d6;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Approach Analysis Styles */
.approach-analysis {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.analysis-section h4 {
    color: white;
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

.analysis-section p {
    margin: 0;
    line-height: 1.5;
}

.analysis-section ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.analysis-section li {
    margin-bottom: 4px;
    line-height: 1.4;
}

/* Test Cases Styles */
.test-cases {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.test-case {
    background: #16213e;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.test-case h5 {
    color: white;
    font-size: 14px;
    margin: 0 0 12px 0;
    font-weight: 600;
}

.test-input, .test-output, .test-explanation {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
}

.test-input {
    color: #4facfe;
}

.test-output {
    color: #43e97b;
}

.test-explanation {
    color: #8892b0;
    font-style: italic;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
