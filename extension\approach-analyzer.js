// Advanced Approach Analysis System for LeetCode Helper Pro
class ApproachAnalyzer {
    constructor() {
        this.algorithmDatabase = this.initializeAlgorithmDatabase();
        this.complexityAnalyzer = new ComplexityAnalyzer();
        this.patternMatcher = new AlgorithmPatternMatcher();
    }

    async analyzeApproach(problem) {
        const analysis = {
            primaryApproach: null,
            alternativeApproaches: [],
            complexityAnalysis: null,
            keyInsights: [],
            implementationTips: [],
            commonPitfalls: [],
            optimizationOpportunities: []
        };

        // Identify the primary approach
        analysis.primaryApproach = this.identifyPrimaryApproach(problem);
        
        // Find alternative approaches
        analysis.alternativeApproaches = this.findAlternativeApproaches(problem);
        
        // Analyze complexity
        analysis.complexityAnalysis = this.complexityAnalyzer.analyze(problem, analysis.primaryApproach);
        
        // Generate insights and tips
        analysis.keyInsights = this.generateKeyInsights(problem, analysis.primaryApproach);
        analysis.implementationTips = this.getImplementationTips(problem, analysis.primaryApproach);
        analysis.commonPitfalls = this.getCommonPitfalls(problem, analysis.primaryApproach);
        analysis.optimizationOpportunities = this.findOptimizations(problem, analysis.primaryApproach);

        return analysis;
    }

    identifyPrimaryApproach(problem) {
        const problemKey = this.getProblemKey(problem);
        
        // Check if we have specific approach data for this problem
        if (this.algorithmDatabase[problemKey]) {
            return this.algorithmDatabase[problemKey].primary;
        }

        // Use pattern matching to identify approach
        return this.patternMatcher.matchApproach(problem);
    }

    findAlternativeApproaches(problem) {
        const problemKey = this.getProblemKey(problem);
        
        if (this.algorithmDatabase[problemKey]) {
            return this.algorithmDatabase[problemKey].alternatives || [];
        }

        return this.patternMatcher.findAlternatives(problem);
    }

    generateKeyInsights(problem, primaryApproach) {
        const insights = [];
        
        // General insights based on approach type
        const approachInsights = {
            'hash-map': [
                'Trade space for time efficiency',
                'Hash maps provide O(1) average lookup time',
                'Consider hash collisions in worst-case scenarios'
            ],
            'two-pointers': [
                'Reduces space complexity compared to nested loops',
                'Works well with sorted data or specific patterns',
                'Can solve problems in single pass'
            ],
            'sliding-window': [
                'Maintains a window of valid elements',
                'Efficient for substring/subarray problems',
                'Avoids redundant calculations'
            ],
            'dynamic-programming': [
                'Breaks problem into overlapping subproblems',
                'Stores results to avoid recomputation',
                'Can be implemented top-down or bottom-up'
            ],
            'binary-search': [
                'Eliminates half the search space each iteration',
                'Requires sorted or monotonic data',
                'Logarithmic time complexity'
            ],
            'dfs': [
                'Explores as far as possible before backtracking',
                'Uses stack (implicit with recursion or explicit)',
                'Good for path-finding and connectivity problems'
            ],
            'bfs': [
                'Explores level by level',
                'Uses queue data structure',
                'Finds shortest path in unweighted graphs'
            ]
        };

        insights.push(...(approachInsights[primaryApproach.algorithm] || []));

        // Problem-specific insights
        if (problem.difficulty === 'Hard') {
            insights.push('Complex problems often combine multiple algorithms');
        }

        if (problem.category === 'Array') {
            insights.push('Consider edge cases like empty arrays and single elements');
        }

        return insights;
    }

    getImplementationTips(problem, primaryApproach) {
        const tips = [];
        
        const algorithmTips = {
            'hash-map': [
                'Initialize hash map before the main loop',
                'Check if key exists before accessing',
                'Consider using defaultdict in Python'
            ],
            'two-pointers': [
                'Initialize pointers at appropriate positions',
                'Define clear movement conditions',
                'Handle edge cases when pointers meet'
            ],
            'sliding-window': [
                'Maintain window invariants',
                'Use while loops for window adjustment',
                'Track window size and contents'
            ],
            'dynamic-programming': [
                'Define base cases clearly',
                'Choose appropriate data structure for memoization',
                'Consider space optimization techniques'
            ],
            'binary-search': [
                'Handle integer overflow in mid calculation',
                'Define search boundaries carefully',
                'Consider iterative vs recursive implementation'
            ]
        };

        tips.push(...(algorithmTips[primaryApproach.algorithm] || []));

        // Language-specific tips
        tips.push('Use meaningful variable names for clarity');
        tips.push('Add comments for complex logic');

        return tips;
    }

    getCommonPitfalls(problem, primaryApproach) {
        const pitfalls = [];
        
        const algorithmPitfalls = {
            'hash-map': [
                'Forgetting to handle duplicate keys',
                'Not considering hash collision performance',
                'Modifying map while iterating'
            ],
            'two-pointers': [
                'Incorrect pointer initialization',
                'Infinite loops due to wrong movement logic',
                'Not handling edge cases properly'
            ],
            'sliding-window': [
                'Not maintaining window invariants',
                'Incorrect window size calculation',
                'Missing edge cases for small inputs'
            ],
            'dynamic-programming': [
                'Incorrect base case definition',
                'Stack overflow in recursive solutions',
                'Not optimizing space complexity'
            ],
            'binary-search': [
                'Off-by-one errors in boundary conditions',
                'Integer overflow in mid calculation',
                'Incorrect termination conditions'
            ]
        };

        pitfalls.push(...(algorithmPitfalls[primaryApproach.algorithm] || []));

        // General pitfalls
        pitfalls.push('Not testing with edge cases');
        pitfalls.push('Ignoring time/space complexity requirements');

        return pitfalls;
    }

    findOptimizations(problem, primaryApproach) {
        const optimizations = [];
        
        // Space optimizations
        if (primaryApproach.spaceComplexity !== 'O(1)') {
            optimizations.push({
                type: 'space',
                description: 'Consider in-place operations to reduce space complexity',
                impact: 'Reduces memory usage'
            });
        }

        // Time optimizations
        if (primaryApproach.timeComplexity.includes('n²')) {
            optimizations.push({
                type: 'time',
                description: 'Look for ways to avoid nested loops',
                impact: 'Improves performance for large inputs'
            });
        }

        // Algorithm-specific optimizations
        const algorithmOptimizations = {
            'hash-map': [
                {
                    type: 'implementation',
                    description: 'Use appropriate hash map size to minimize collisions',
                    impact: 'Better average-case performance'
                }
            ],
            'dynamic-programming': [
                {
                    type: 'space',
                    description: 'Use rolling array technique if only previous states are needed',
                    impact: 'Reduces space from O(n) to O(1)'
                }
            ]
        };

        const specificOptimizations = algorithmOptimizations[primaryApproach.algorithm] || [];
        optimizations.push(...specificOptimizations);

        return optimizations;
    }

    getProblemKey(problem) {
        return problem.slug || problem.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    initializeAlgorithmDatabase() {
        return {
            'two-sum': {
                primary: {
                    name: 'Hash Map Lookup',
                    algorithm: 'hash-map',
                    timeComplexity: 'O(n)',
                    spaceComplexity: 'O(n)',
                    description: 'Use hash map to store complements and find pairs in single pass'
                },
                alternatives: [
                    {
                        name: 'Brute Force',
                        algorithm: 'nested-loops',
                        timeComplexity: 'O(n²)',
                        spaceComplexity: 'O(1)',
                        description: 'Check all pairs using nested loops'
                    },
                    {
                        name: 'Two Pointers (if sorted)',
                        algorithm: 'two-pointers',
                        timeComplexity: 'O(n log n)',
                        spaceComplexity: 'O(1)',
                        description: 'Sort array first, then use two pointers'
                    }
                ]
            },
            'add-two-numbers': {
                primary: {
                    name: 'Digit-by-Digit Addition',
                    algorithm: 'simulation',
                    timeComplexity: 'O(max(m,n))',
                    spaceComplexity: 'O(max(m,n))',
                    description: 'Simulate addition process with carry handling'
                }
            },
            'longest-substring-without-repeating-characters': {
                primary: {
                    name: 'Sliding Window',
                    algorithm: 'sliding-window',
                    timeComplexity: 'O(n)',
                    spaceComplexity: 'O(min(m,n))',
                    description: 'Use sliding window with character set tracking'
                },
                alternatives: [
                    {
                        name: 'Brute Force',
                        algorithm: 'nested-loops',
                        timeComplexity: 'O(n³)',
                        spaceComplexity: 'O(min(m,n))',
                        description: 'Check all substrings for uniqueness'
                    }
                ]
            },
            'valid-parentheses': {
                primary: {
                    name: 'Stack Matching',
                    algorithm: 'stack',
                    timeComplexity: 'O(n)',
                    spaceComplexity: 'O(n)',
                    description: 'Use stack to match opening and closing brackets'
                }
            },
            'merge-two-sorted-lists': {
                primary: {
                    name: 'Two Pointers Merge',
                    algorithm: 'two-pointers',
                    timeComplexity: 'O(m+n)',
                    spaceComplexity: 'O(1)',
                    description: 'Compare elements from both lists and merge'
                },
                alternatives: [
                    {
                        name: 'Recursive Merge',
                        algorithm: 'recursion',
                        timeComplexity: 'O(m+n)',
                        spaceComplexity: 'O(m+n)',
                        description: 'Recursively merge smaller elements first'
                    }
                ]
            }
        };
    }
}

// Complexity Analysis System
class ComplexityAnalyzer {
    analyze(problem, approach) {
        return {
            time: {
                complexity: approach.timeComplexity,
                explanation: this.explainTimeComplexity(approach),
                bestCase: this.getBestCase(approach),
                averageCase: this.getAverageCase(approach),
                worstCase: this.getWorstCase(approach)
            },
            space: {
                complexity: approach.spaceComplexity,
                explanation: this.explainSpaceComplexity(approach),
                auxiliary: this.getAuxiliarySpace(approach),
                total: this.getTotalSpace(approach)
            },
            scalability: this.analyzeScalability(approach)
        };
    }

    explainTimeComplexity(approach) {
        const explanations = {
            'O(1)': 'Constant time - operation takes same time regardless of input size',
            'O(log n)': 'Logarithmic time - eliminates half the possibilities each step',
            'O(n)': 'Linear time - processes each element once',
            'O(n log n)': 'Linearithmic time - typical of efficient sorting algorithms',
            'O(n²)': 'Quadratic time - nested loops over input',
            'O(2^n)': 'Exponential time - explores all possible combinations'
        };
        
        return explanations[approach.timeComplexity] || 'Time complexity analysis';
    }

    explainSpaceComplexity(approach) {
        const explanations = {
            'O(1)': 'Constant space - uses fixed amount of extra memory',
            'O(log n)': 'Logarithmic space - typically from recursion stack',
            'O(n)': 'Linear space - stores data proportional to input size',
            'O(n²)': 'Quadratic space - 2D data structures or nested storage'
        };
        
        return explanations[approach.spaceComplexity] || 'Space complexity analysis';
    }

    getBestCase(approach) {
        return approach.timeComplexity; // Simplified - could be more sophisticated
    }

    getAverageCase(approach) {
        return approach.timeComplexity;
    }

    getWorstCase(approach) {
        return approach.timeComplexity;
    }

    getAuxiliarySpace(approach) {
        return approach.spaceComplexity;
    }

    getTotalSpace(approach) {
        return approach.spaceComplexity;
    }

    analyzeScalability(approach) {
        const timeComplexity = approach.timeComplexity;
        
        if (timeComplexity.includes('1')) return 'Excellent - scales to any input size';
        if (timeComplexity.includes('log')) return 'Very good - scales well with large inputs';
        if (timeComplexity.includes('n²')) return 'Poor - may struggle with large inputs';
        if (timeComplexity.includes('2^n')) return 'Very poor - only suitable for small inputs';
        
        return 'Good - reasonable performance for most inputs';
    }
}

// Algorithm Pattern Matching System
class AlgorithmPatternMatcher {
    matchApproach(problem) {
        const title = problem.title.toLowerCase();
        const description = (problem.description || '').toLowerCase();
        const text = title + ' ' + description;

        // Pattern matching rules for primary approach
        if (text.includes('two sum') || text.includes('target sum')) {
            return {
                name: 'Hash Map Lookup',
                algorithm: 'hash-map',
                timeComplexity: 'O(n)',
                spaceComplexity: 'O(n)'
            };
        }

        if (text.includes('palindrome') || text.includes('reverse')) {
            return {
                name: 'Two Pointers',
                algorithm: 'two-pointers',
                timeComplexity: 'O(n)',
                spaceComplexity: 'O(1)'
            };
        }

        if (text.includes('substring') || text.includes('subarray')) {
            return {
                name: 'Sliding Window',
                algorithm: 'sliding-window',
                timeComplexity: 'O(n)',
                spaceComplexity: 'O(k)'
            };
        }

        // Default approach
        return {
            name: 'Hash Map Approach',
            algorithm: 'hash-map',
            timeComplexity: 'O(n)',
            spaceComplexity: 'O(n)'
        };
    }

    findAlternatives(problem) {
        // Return common alternative approaches
        return [
            {
                name: 'Brute Force',
                algorithm: 'nested-loops',
                timeComplexity: 'O(n²)',
                spaceComplexity: 'O(1)',
                description: 'Simple but inefficient approach'
            }
        ];
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApproachAnalyzer, ComplexityAnalyzer, AlgorithmPatternMatcher };
}
