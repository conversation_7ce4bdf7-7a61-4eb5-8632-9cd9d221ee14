"""
Intelligent Hint Generation Service using AI
"""

from typing import Dict, List, Optional, Any
import asyncio
import logging
from datetime import datetime, timedelta

from app.services.ai_service import AIService
from app.services.difficulty_detector import DifficultyDetector
from app.models.schemas import (
    ProblemData, HintRequest, HintResponse, UserContext,
    HintLevel, StruggleDetectionRequest, StruggleDetectionResponse
)

logger = logging.getLogger(__name__)


class IntelligentHintService:
    """Service for generating intelligent, progressive hints"""
    
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        self.hint_history = {}  # Store hint history per session
        self.difficulty_detector = DifficultyDetector()
        
    async def generate_progressive_hint(self, hint_request: HintRequest) -> HintResponse:
        """Generate a progressive hint based on user context and history"""
        
        try:
            # Analyze user context and adjust hint accordingly
            enhanced_request = await self._enhance_hint_request(hint_request)
            
            # Generate hint using AI service
            hint_data = await self.ai_service.generate_hint(enhanced_request)
            
            # Post-process hint for better educational value
            processed_hint = await self._post_process_hint(hint_data, enhanced_request)
            
            # Store hint in history
            await self._store_hint_history(enhanced_request, processed_hint)
            
            # Create response
            response = HintResponse(
                hint_text=processed_hint['hint_text'],
                level=enhanced_request.level,
                follow_up_questions=processed_hint.get('follow_up_questions', []),
                key_concepts=processed_hint.get('key_concepts', []),
                next_steps=processed_hint.get('next_steps', []),
                encouragement=processed_hint.get('encouragement', ''),
                generated_at=datetime.utcnow(),
                model=processed_hint.get('model', 'gemini-pro')
            )
            
            logger.info(f"Generated {enhanced_request.level} hint for {enhanced_request.problem_data.title}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate progressive hint: {e}")
            return self._create_fallback_hint(hint_request)
    
    async def _enhance_hint_request(self, hint_request: HintRequest) -> HintRequest:
        """Enhance hint request with context and history"""
        
        # Get session history
        session_id = hint_request.user_context.session_id if hint_request.user_context else None
        history = self.hint_history.get(session_id, {}).get(hint_request.problem_data.slug, [])
        
        # Create enhanced user context
        enhanced_context = hint_request.user_context or UserContext()
        enhanced_context.previous_hints = [h['hint_text'] for h in history]
        enhanced_context.hint_count = len(history)
        
        # Adjust hint level based on history and struggle detection
        adjusted_level = await self._adjust_hint_level(hint_request, history)
        
        # Create enhanced request
        enhanced_request = HintRequest(
            problem_data=hint_request.problem_data,
            level=adjusted_level,
            user_context=enhanced_context,
            custom_prompt=hint_request.custom_prompt
        )
        
        return enhanced_request
    
    async def _adjust_hint_level(self, hint_request: HintRequest, history: List[Dict]) -> HintLevel:
        """Adjust hint level based on user context and history"""
        
        original_level = hint_request.level
        
        # If user has requested many hints, consider upgrading level
        if len(history) >= 3 and original_level == HintLevel.GENTLE:
            logger.info("Upgrading hint level due to multiple requests")
            return HintLevel.MODERATE
        elif len(history) >= 5 and original_level == HintLevel.MODERATE:
            logger.info("Upgrading hint level due to many requests")
            return HintLevel.STRONG
        
        # Check for struggle indicators using advanced difficulty detector
        if hint_request.user_context:
            struggle_request = StruggleDetectionRequest(
                problem_data=hint_request.problem_data,
                user_context=hint_request.user_context,
                interaction_history=history
            )

            struggle_response = await self.difficulty_detector.detect_struggle(struggle_request)

            if struggle_response.is_struggling and struggle_response.struggle_score > 0.7:
                # User is struggling significantly, upgrade hint level
                logger.info(f"Upgrading hint level due to struggle detection: score={struggle_response.struggle_score:.2f}")
                if original_level == HintLevel.GENTLE:
                    return HintLevel.MODERATE
                elif original_level == HintLevel.MODERATE:
                    return HintLevel.STRONG
        
        return original_level
    
    async def _post_process_hint(self, hint_data: Dict[str, Any], hint_request: HintRequest) -> Dict[str, Any]:
        """Post-process hint for better educational value"""
        
        processed_hint = hint_data.copy()
        
        # Add language-specific tips if language is specified
        if hint_request.user_context and hint_request.user_context.language:
            language_tips = self._get_language_specific_tips(
                hint_request.user_context.language,
                hint_request.problem_data
            )
            if language_tips:
                processed_hint['language_tips'] = language_tips
        
        # Add progressive learning elements
        processed_hint = self._add_progressive_elements(processed_hint, hint_request)
        
        # Ensure hint doesn't contain code
        processed_hint['hint_text'] = self._sanitize_hint_text(processed_hint['hint_text'])
        
        return processed_hint
    
    def _get_language_specific_tips(self, language: str, problem_data: ProblemData) -> List[str]:
        """Get language-specific tips for the problem"""
        
        language_tips = {
            'python': [
                "Use dict() for hash maps and list() for dynamic arrays",
                "Consider list comprehensions for concise code",
                "Remember that Python has built-in functions like enumerate() and zip()"
            ],
            'javascript': [
                "Use Map() for hash maps instead of plain objects",
                "Array methods like filter(), map(), reduce() are very useful",
                "Consider using Set for unique values"
            ],
            'java': [
                "Use HashMap<> for hash maps and ArrayList<> for dynamic arrays",
                "Remember to handle null values properly",
                "Consider using Collections utility methods"
            ],
            'cpp': [
                "Use unordered_map for hash maps and vector for dynamic arrays",
                "Be careful with memory management and pointers",
                "STL algorithms can simplify your code"
            ]
        }
        
        return language_tips.get(language.lower(), [])
    
    def _add_progressive_elements(self, hint_data: Dict[str, Any], hint_request: HintRequest) -> Dict[str, Any]:
        """Add progressive learning elements to hint"""
        
        # Add difficulty-appropriate guidance
        if hint_request.level == HintLevel.GENTLE:
            hint_data['learning_focus'] = "Think about the problem step by step"
            hint_data['confidence_builder'] = "You're on the right track! Take your time to understand the problem."
        elif hint_request.level == HintLevel.MODERATE:
            hint_data['learning_focus'] = "Consider the algorithm and data structures involved"
            hint_data['confidence_builder'] = "You're making progress! Think about the approach systematically."
        else:  # STRONG
            hint_data['learning_focus'] = "Focus on the implementation strategy"
            hint_data['confidence_builder'] = "You're almost there! Break down the solution into clear steps."
        
        return hint_data
    
    def _sanitize_hint_text(self, hint_text: str) -> str:
        """Ensure hint doesn't contain actual code"""
        
        # Remove common code patterns
        code_patterns = [
            r'def\s+\w+\s*\(',  # Python function definitions
            r'function\s+\w+\s*\(',  # JavaScript function definitions
            r'public\s+\w+\s+\w+\s*\(',  # Java method definitions
            r'for\s*\(\s*\w+.*\)\s*{',  # For loops
            r'while\s*\(\s*.*\)\s*{',  # While loops
            r'if\s*\(\s*.*\)\s*{',  # If statements
        ]
        
        import re
        sanitized_text = hint_text
        
        for pattern in code_patterns:
            sanitized_text = re.sub(pattern, '[CODE_REMOVED]', sanitized_text, flags=re.IGNORECASE)
        
        # If too much code was removed, provide a generic hint
        if sanitized_text.count('[CODE_REMOVED]') > 2:
            sanitized_text = "Focus on understanding the algorithm and approach rather than specific implementation details. Think about what data structures and techniques would be most effective for this problem."
        
        return sanitized_text
    
    async def _store_hint_history(self, hint_request: HintRequest, hint_data: Dict[str, Any]):
        """Store hint in session history"""
        
        if not hint_request.user_context or not hint_request.user_context.session_id:
            return
        
        session_id = hint_request.user_context.session_id
        problem_slug = hint_request.problem_data.slug
        
        if session_id not in self.hint_history:
            self.hint_history[session_id] = {}
        
        if problem_slug not in self.hint_history[session_id]:
            self.hint_history[session_id][problem_slug] = []
        
        hint_entry = {
            'level': hint_request.level,
            'hint_text': hint_data['hint_text'],
            'timestamp': datetime.utcnow().isoformat(),
            'concepts': hint_data.get('key_concepts', [])
        }
        
        self.hint_history[session_id][problem_slug].append(hint_entry)
        
        # Limit history size
        if len(self.hint_history[session_id][problem_slug]) > 10:
            self.hint_history[session_id][problem_slug] = self.hint_history[session_id][problem_slug][-10:]
    
    def _create_fallback_hint(self, hint_request: HintRequest) -> HintResponse:
        """Create fallback hint when AI service fails"""
        
        fallback_hints = {
            HintLevel.GENTLE: "Think about what data structures might be helpful for this problem. Consider the constraints and what operations you need to perform efficiently.",
            HintLevel.MODERATE: "Consider breaking this problem down into smaller steps. What algorithm patterns have you seen that might apply here?",
            HintLevel.STRONG: "Focus on the optimal approach for this problem type. Think about time and space complexity, and consider if there are any standard algorithms that apply."
        }
        
        return HintResponse(
            hint_text=fallback_hints.get(hint_request.level, fallback_hints[HintLevel.GENTLE]),
            level=hint_request.level,
            follow_up_questions=["What approach would be most efficient?"],
            key_concepts=["Problem Analysis"],
            next_steps=["Break down the problem into smaller parts"],
            encouragement="Keep thinking step by step - you can solve this!",
            generated_at=datetime.utcnow(),
            model="fallback"
        )


    async def detect_struggle(self, request: StruggleDetectionRequest) -> StruggleDetectionResponse:
        """Detect user struggle using the advanced difficulty detector"""
        return await self.difficulty_detector.detect_struggle(request)
