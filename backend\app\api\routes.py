"""
API Routes for LeetCode Helper Pro AI Backend
"""

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Dict, Any
import logging
import time
import uuid

from app.models.schemas import (
    ProblemData, HintRequest, HintResponse, AnalysisRequest, AnalysisResponse,
    YouTubeRequest, YouTubeResponse, StruggleDetectionRequest, StruggleDetectionResponse,
    UserInteractionLog, HealthCheckResponse, ErrorResponse
)
from app.services.ai_service import AIService
from app.services.youtube_service import YouTubeService
from app.services.hint_service import IntelligentHintService
from app.core.database import get_db, DatabaseManager

logger = logging.getLogger(__name__)

# Create API router
api_router = APIRouter()

# Global services (will be injected)
ai_service = None
youtube_service = None
hint_service = None


def get_services():
    """Get service instances"""
    global ai_service, youtube_service, hint_service
    
    if not ai_service:
        from main import app
        ai_service = app.state.ai_service
        youtube_service = app.state.youtube_service
        hint_service = IntelligentHintService(ai_service) if ai_service else None
    
    return ai_service, youtube_service, hint_service


@api_router.post("/analyze", response_model=AnalysisResponse)
async def analyze_problem(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks,
    db=Depends(get_db)
):
    """Analyze a LeetCode problem using AI"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        ai_svc, _, _ = get_services()
        
        if not ai_svc or not ai_svc.is_ready():
            raise HTTPException(status_code=503, detail="AI service not available")
        
        logger.info(f"Analyzing problem: {request.problem_data.title}")
        
        # Generate analysis using AI
        analysis_data = await ai_svc.analyze_problem(request.problem_data)
        
        # Create response
        response = AnalysisResponse(
            problem_type=analysis_data.get('problem_type', 'General'),
            key_concepts=analysis_data.get('key_concepts', []),
            optimal_approach=analysis_data.get('optimal_approach', {}),
            alternative_approaches=analysis_data.get('alternative_approaches', []),
            key_insights=analysis_data.get('key_insights', []),
            common_pitfalls=analysis_data.get('common_pitfalls', []),
            edge_cases=analysis_data.get('edge_cases', []),
            difficulty_justification=analysis_data.get('difficulty_justification', '')
        )
        
        # Store analysis in background
        background_tasks.add_task(
            DatabaseManager.save_problem_analysis,
            db,
            request.problem_data.slug,
            {
                'title': request.problem_data.title,
                'difficulty': request.problem_data.difficulty,
                'category': request.problem_data.category,
                'analysis': analysis_data
            }
        )
        
        # Log performance
        response_time = (time.time() - start_time) * 1000
        logger.info(f"Analysis completed in {response_time:.2f}ms for {request.problem_data.title}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error analyzing problem: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@api_router.post("/hint", response_model=HintResponse)
async def generate_hint(
    request: HintRequest,
    background_tasks: BackgroundTasks,
    db=Depends(get_db)
):
    """Generate an intelligent hint for a problem"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        ai_svc, _, hint_svc = get_services()
        
        if not hint_svc:
            raise HTTPException(status_code=503, detail="Hint service not available")
        
        logger.info(f"Generating {request.level} hint for: {request.problem_data.title}")
        
        # Generate progressive hint
        response = await hint_svc.generate_progressive_hint(request)
        
        # Store hint in background
        background_tasks.add_task(
            DatabaseManager.save_hint_generation,
            db,
            request.problem_data.slug,
            {
                'level': request.level,
                'content': response.hint_text,
                'context': request.user_context.dict() if request.user_context else {},
                'language': request.user_context.language if request.user_context else None,
                'model': response.model
            }
        )
        
        # Log performance
        response_time = (time.time() - start_time) * 1000
        logger.info(f"Hint generated in {response_time:.2f}ms")
        
        return response
        
    except Exception as e:
        logger.error(f"Error generating hint: {e}")
        raise HTTPException(status_code=500, detail=f"Hint generation failed: {str(e)}")


@api_router.post("/youtube", response_model=YouTubeResponse)
async def find_youtube_videos(
    request: YouTubeRequest,
    background_tasks: BackgroundTasks,
    db=Depends(get_db)
):
    """Find relevant YouTube solution videos"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        _, youtube_svc, _ = get_services()
        
        if not youtube_svc or not youtube_svc.is_ready():
            raise HTTPException(status_code=503, detail="YouTube service not available")
        
        logger.info(f"Finding YouTube videos for: {request.problem_data.title}")
        
        # Find videos
        response = await youtube_svc.find_solution_videos(request)
        
        # Store recommendations in background
        for video in response.videos:
            background_tasks.add_task(
                DatabaseManager.save_youtube_recommendation,
                db,
                request.problem_data.slug,
                {
                    'video_id': video.video_id,
                    'title': video.title,
                    'channel': video.channel_name,
                    'url': video.url,
                    'view_count': video.view_count,
                    'duration': video.duration,
                    'relevance_score': video.relevance_score
                }
            )
        
        # Log performance
        response_time = (time.time() - start_time) * 1000
        logger.info(f"Found {len(response.videos)} videos in {response_time:.2f}ms")
        
        return response
        
    except Exception as e:
        logger.error(f"Error finding YouTube videos: {e}")
        raise HTTPException(status_code=500, detail=f"YouTube search failed: {str(e)}")


@api_router.post("/struggle-detection", response_model=StruggleDetectionResponse)
async def detect_struggle(request: StruggleDetectionRequest):
    """Detect if user is struggling with a problem"""
    
    try:
        _, _, hint_svc = get_services()
        
        if not hint_svc:
            raise HTTPException(status_code=503, detail="Hint service not available")
        
        logger.info(f"Analyzing struggle for: {request.problem_data.title}")
        
        # Detect struggle
        response = await hint_svc.struggle_detector.detect_struggle(request)
        
        logger.info(f"Struggle detection: score={response.struggle_score:.2f}, struggling={response.is_struggling}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error detecting struggle: {e}")
        raise HTTPException(status_code=500, detail=f"Struggle detection failed: {str(e)}")


@api_router.post("/interaction")
async def log_interaction(
    interaction: UserInteractionLog,
    background_tasks: BackgroundTasks,
    request: Request,
    db=Depends(get_db)
):
    """Log user interaction for analytics"""
    
    try:
        # Add request metadata
        interaction_data = interaction.dict()
        interaction_data['user_agent'] = request.headers.get('user-agent', '')
        interaction_data['ip_address'] = request.client.host
        
        # Store interaction in background
        background_tasks.add_task(
            DatabaseManager.track_user_interaction,
            db,
            interaction_data
        )
        
        return {"status": "logged"}
        
    except Exception as e:
        logger.error(f"Error logging interaction: {e}")
        return {"status": "error", "message": str(e)}


@api_router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    
    try:
        ai_svc, youtube_svc, hint_svc = get_services()
        
        services = {
            "ai_service": {
                "service_name": "AI Service",
                "is_healthy": ai_svc.is_ready() if ai_svc else False,
                "last_check": time.time(),
                "total_requests": ai_svc.request_count if ai_svc else 0,
                "error_rate": 0.0,
                "average_response_time": 0.0
            },
            "youtube_service": {
                "service_name": "YouTube Service", 
                "is_healthy": youtube_svc.is_ready() if youtube_svc else False,
                "last_check": time.time(),
                "total_requests": youtube_svc.request_count if youtube_svc else 0,
                "error_rate": 0.0,
                "average_response_time": 0.0
            },
            "hint_service": {
                "service_name": "Hint Service",
                "is_healthy": hint_svc is not None,
                "last_check": time.time(),
                "total_requests": 0,
                "error_rate": 0.0,
                "average_response_time": 0.0
            }
        }
        
        overall_healthy = all(service["is_healthy"] for service in services.values())
        
        return HealthCheckResponse(
            status="healthy" if overall_healthy else "degraded",
            version="1.0.0",
            services=services
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@api_router.get("/stats")
async def get_stats():
    """Get service statistics"""
    
    try:
        ai_svc, youtube_svc, hint_svc = get_services()
        
        stats = {
            "ai_service": ai_svc.get_stats() if ai_svc else {},
            "youtube_service": youtube_svc.get_stats() if youtube_svc else {},
            "hint_service": {"available": hint_svc is not None}
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stats")


# Error handlers
@api_router.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error="HTTP_ERROR",
            message=exc.detail,
            details={"status_code": exc.status_code},
            request_id=str(uuid.uuid4())
        ).dict()
    )


@api_router.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="INTERNAL_ERROR",
            message="An internal error occurred",
            details={"exception_type": type(exc).__name__},
            request_id=str(uuid.uuid4())
        ).dict()
    )
