# LeetCode Helper Pro 🧠

A comprehensive browser extension that provides intelligent assistance for solving LeetCode problems with a beautiful, modern interface.

## ✨ Features

### 🎯 3-Level Hint System
- **Gentle Hints**: Subtle guidance to nudge you in the right direction
- **Moderate Hints**: Clear direction without giving away the solution
- **Strong Hints**: Detailed step-by-step approach

### 📊 Approach Analysis
- Algorithm categorization and pattern recognition
- Time and space complexity breakdown
- Alternative solution approaches
- Key insights and optimization opportunities

### 🧪 Test Case Generation
- Automatic generation of basic, edge, and performance test cases
- Problem-specific test scenarios
- Input validation and expected outputs

### 🌍 Multi-Language Support
- Support for 10+ programming languages
- Language-specific hints and tips
- Code templates and syntax highlighting
- Best practices for each language

### 📈 Progress Tracking
- Comprehensive statistics and analytics
- Badge and achievement system
- Streak tracking and weekly goals
- Difficulty and category breakdowns

### 🎨 Modern UI Design
- Beautiful gradient design with smooth animations
- Responsive layout for different screen sizes
- Professional loading states and micro-interactions
- Dark theme with excellent contrast

### ⚡ Smart Features
- Automatic problem detection on LeetCode pages
- Context menu integration for quick access
- Cross-browser compatibility (Chrome, Firefox, Edge)
- Real-time code analysis and suggestions

## 🚀 Installation

### Chrome/Edge
1. Download or clone this repository
2. Open Chrome/Edge and go to `chrome://extensions/` or `edge://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `extension` folder
5. The extension icon should appear in your toolbar

### Firefox
1. Download or clone this repository
2. Open Firefox and go to `about:debugging`
3. Click "This Firefox" in the sidebar
4. Click "Load Temporary Add-on"
5. Select any file in the `extension` folder (e.g., `manifest.json`)

## 📁 Project Structure

```
extension/
├── manifest.json              # Extension configuration
├── popup.html                 # Main popup interface
├── popup.css                  # Popup styling
├── popup.js                   # Popup functionality
├── content.js                 # Content script for LeetCode pages
├── content.css                # Content script styling
├── background.js              # Background service worker
├── hint-system.js             # Advanced hint generation
├── approach-analyzer.js       # Algorithm analysis system
├── test-case-generator.js     # Test case generation
├── language-support.js        # Multi-language support
├── progress-tracker.js        # Progress tracking system
├── mock-data.js              # Mock data for testing
├── icons/                     # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md                  # This file
```

## 🎮 Usage

### Basic Usage
1. Navigate to any LeetCode problem page
2. Click the extension icon in your toolbar
3. The extension will automatically detect the problem
4. Use the hint buttons to get progressive assistance

### Context Menu
- Right-click on any LeetCode problem page
- Select "LeetCode Helper Pro" from the context menu
- Choose from various quick actions:
  - Gentle/Moderate/Strong hints
  - Approach analysis
  - Test case generation
  - Quick hint panel

### Floating Helper
- A floating helper button appears on LeetCode problem pages
- Click it for quick access to hints and analysis
- Provides inline hints directly on the page

## 🔧 Development

### Prerequisites
- Modern web browser (Chrome 88+, Firefox 78+, Edge 88+)
- Basic understanding of JavaScript, HTML, and CSS

### Local Development
1. Clone the repository
2. Make changes to the extension files
3. Reload the extension in your browser's extension management page
4. Test changes on LeetCode problem pages

### Adding New Features
1. **Hints**: Add to `hint-system.js` and update the hint database
2. **Languages**: Extend `language-support.js` with new language definitions
3. **Test Cases**: Add patterns to `test-case-generator.js`
4. **UI**: Modify `popup.html`, `popup.css`, and `popup.js`

## 🧪 Testing

The extension includes comprehensive mock data for testing:

```javascript
// Access mock data in browser console
window.mockDataProvider.getMockProblem('two-sum');
window.mockDataProvider.getMockHints('two-sum', 'gentle');
window.mockDataProvider.getMockTestCases('two-sum');
```

### Test Scenarios
- New user experience
- Experienced user with progress
- Different problem difficulties
- Various programming languages

## 🎨 Customization

### Themes
The extension uses CSS custom properties for easy theming:

```css
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-primary: #1a1a2e;
  --text-primary: #ffffff;
  /* ... more variables */
}
```

### Adding Languages
To add a new programming language:

1. Update `language-support.js`:
```javascript
'newlang': {
    name: 'New Language',
    extension: '.ext',
    syntax: 'newlang',
    features: ['feature1', 'feature2'],
    commonDataStructures: ['struct1', 'struct2'],
    tips: ['tip1', 'tip2']
}
```

2. Add language-specific hints and templates

## 🐛 Troubleshooting

### Common Issues

**Extension not loading:**
- Check that all required files are present
- Ensure manifest.json is valid
- Check browser console for errors

**Problem not detected:**
- Refresh the LeetCode page
- Check if URL matches the pattern in manifest.json
- Verify content script is injected

**Hints not working:**
- Check background script console for errors
- Verify mock data is loaded
- Test with different problems

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('leetcode-helper-debug', 'true');
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Contribution Guidelines
- Follow existing code style
- Add comments for complex logic
- Test on multiple browsers
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- LeetCode for providing the platform
- Chrome Extensions API documentation
- Open source libraries and tools used
- Community feedback and suggestions

## 📞 Support

For issues, questions, or suggestions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review existing issues for solutions

---

**Happy Coding! 🚀**

*LeetCode Helper Pro - Your intelligent coding companion*
