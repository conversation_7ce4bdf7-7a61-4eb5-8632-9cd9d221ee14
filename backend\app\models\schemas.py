"""
Pydantic schemas for LeetCode Helper Pro AI Backend
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class HintLevel(str, Enum):
    """Hint difficulty levels"""
    GENTLE = "gentle"
    MODERATE = "moderate"
    STRONG = "strong"


class ProblemDifficulty(str, Enum):
    """LeetCode problem difficulties"""
    EASY = "Easy"
    MEDIUM = "Medium"
    HARD = "Hard"


class InteractionType(str, Enum):
    """Types of user interactions"""
    HINT_REQUEST = "hint_request"
    ANALYSIS_REQUEST = "analysis_request"
    YOUTUBE_REQUEST = "youtube_request"
    PROBLEM_VIEW = "problem_view"


class ProblemData(BaseModel):
    """LeetCode problem data"""
    title: str = Field(..., description="Problem title")
    slug: str = Field(..., description="Problem slug/identifier")
    difficulty: ProblemDifficulty = Field(..., description="Problem difficulty")
    category: Optional[str] = Field(None, description="Problem category")
    description: str = Field(..., description="Problem description")
    constraints: Optional[List[str]] = Field(None, description="Problem constraints")
    tags: Optional[List[str]] = Field(None, description="Problem tags")
    acceptance_rate: Optional[str] = Field(None, description="Acceptance rate")
    url: Optional[str] = Field(None, description="Problem URL")
    
    @validator('title', 'description')
    def validate_non_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Field cannot be empty')
        return v.strip()


class UserContext(BaseModel):
    """User context for personalized hints"""
    session_id: Optional[str] = Field(None, description="User session ID")
    language: Optional[str] = Field(None, description="Preferred programming language")
    previous_hints: Optional[List[str]] = Field(None, description="Previously given hints")
    time_spent: Optional[int] = Field(None, description="Time spent on problem (minutes)")
    hint_count: Optional[int] = Field(0, description="Number of hints requested")
    struggle_indicators: Optional[List[str]] = Field(None, description="Signs of user struggling")
    skill_level: Optional[str] = Field(None, description="User skill level")


class HintRequest(BaseModel):
    """Request for AI-generated hint"""
    problem_data: ProblemData = Field(..., description="Problem information")
    level: HintLevel = Field(..., description="Hint difficulty level")
    user_context: Optional[UserContext] = Field(None, description="User context")
    custom_prompt: Optional[str] = Field(None, description="Custom hint prompt")


class HintResponse(BaseModel):
    """AI-generated hint response"""
    hint_text: str = Field(..., description="Main hint content")
    level: HintLevel = Field(..., description="Hint level")
    follow_up_questions: Optional[List[str]] = Field(None, description="Follow-up questions")
    key_concepts: Optional[List[str]] = Field(None, description="Key concepts involved")
    next_steps: Optional[List[str]] = Field(None, description="Suggested next steps")
    encouragement: Optional[str] = Field(None, description="Encouraging message")
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    model: str = Field(default="gemini-pro", description="AI model used")


class AnalysisRequest(BaseModel):
    """Request for problem analysis"""
    problem_data: ProblemData = Field(..., description="Problem information")
    analysis_type: Optional[str] = Field("comprehensive", description="Type of analysis")
    include_approaches: bool = Field(True, description="Include alternative approaches")
    include_complexity: bool = Field(True, description="Include complexity analysis")


class ApproachInfo(BaseModel):
    """Information about a solution approach"""
    name: str = Field(..., description="Approach name")
    description: str = Field(..., description="Approach description")
    time_complexity: str = Field(..., description="Time complexity")
    space_complexity: str = Field(..., description="Space complexity")
    pros: Optional[List[str]] = Field(None, description="Advantages")
    cons: Optional[List[str]] = Field(None, description="Disadvantages")


class AnalysisResponse(BaseModel):
    """AI-generated problem analysis"""
    problem_type: str = Field(..., description="Type of problem")
    key_concepts: List[str] = Field(..., description="Key concepts")
    optimal_approach: ApproachInfo = Field(..., description="Optimal solution approach")
    alternative_approaches: Optional[List[ApproachInfo]] = Field(None, description="Alternative approaches")
    key_insights: Optional[List[str]] = Field(None, description="Key insights")
    common_pitfalls: Optional[List[str]] = Field(None, description="Common mistakes")
    edge_cases: Optional[List[str]] = Field(None, description="Edge cases to consider")
    difficulty_justification: Optional[str] = Field(None, description="Why this difficulty level")
    generated_at: datetime = Field(default_factory=datetime.utcnow)


class YouTubeVideo(BaseModel):
    """YouTube video information"""
    video_id: str = Field(..., description="YouTube video ID")
    title: str = Field(..., description="Video title")
    channel_name: str = Field(..., description="Channel name")
    url: str = Field(..., description="Video URL")
    thumbnail_url: Optional[str] = Field(None, description="Thumbnail URL")
    duration: Optional[str] = Field(None, description="Video duration")
    view_count: Optional[int] = Field(None, description="View count")
    like_count: Optional[int] = Field(None, description="Like count")
    published_at: Optional[datetime] = Field(None, description="Publication date")
    relevance_score: float = Field(default=0.0, description="Relevance score (0-1)")


class YouTubeRequest(BaseModel):
    """Request for YouTube video recommendations"""
    problem_data: ProblemData = Field(..., description="Problem information")
    max_results: int = Field(default=5, ge=1, le=20, description="Maximum number of results")
    language_preference: Optional[str] = Field(None, description="Preferred explanation language")
    difficulty_filter: Optional[str] = Field(None, description="Filter by explanation difficulty")


class YouTubeResponse(BaseModel):
    """YouTube video recommendations response"""
    videos: List[YouTubeVideo] = Field(..., description="Recommended videos")
    search_query: str = Field(..., description="Search query used")
    total_results: int = Field(..., description="Total results found")
    generated_at: datetime = Field(default_factory=datetime.utcnow)


class StruggleDetectionRequest(BaseModel):
    """Request for struggle detection analysis"""
    problem_data: ProblemData = Field(..., description="Problem information")
    user_context: UserContext = Field(..., description="User interaction context")
    interaction_history: Optional[List[Dict[str, Any]]] = Field(None, description="User interaction history")


class StruggleDetectionResponse(BaseModel):
    """Struggle detection analysis response"""
    is_struggling: bool = Field(..., description="Whether user appears to be struggling")
    struggle_score: float = Field(..., ge=0.0, le=1.0, description="Struggle intensity (0-1)")
    indicators: List[str] = Field(..., description="Detected struggle indicators")
    recommendations: List[str] = Field(..., description="Recommended actions")
    suggest_youtube: bool = Field(..., description="Whether to suggest YouTube videos")


class UserInteractionLog(BaseModel):
    """Log entry for user interaction"""
    session_id: str = Field(..., description="User session ID")
    problem_slug: str = Field(..., description="Problem identifier")
    interaction_type: InteractionType = Field(..., description="Type of interaction")
    interaction_data: Dict[str, Any] = Field(..., description="Interaction details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_agent: Optional[str] = Field(None, description="User agent string")
    ip_address: Optional[str] = Field(None, description="User IP address")


class AIServiceStatus(BaseModel):
    """AI service status information"""
    service_name: str = Field(..., description="Service name")
    is_healthy: bool = Field(..., description="Service health status")
    last_check: datetime = Field(..., description="Last health check time")
    total_requests: int = Field(default=0, description="Total requests processed")
    error_rate: float = Field(default=0.0, description="Error rate percentage")
    average_response_time: float = Field(default=0.0, description="Average response time (ms)")


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Overall status")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    services: Dict[str, AIServiceStatus] = Field(..., description="Individual service statuses")


class ErrorResponse(BaseModel):
    """Error response schema"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(None, description="Request ID for tracking")


# Configuration schemas
class AIConfig(BaseModel):
    """AI service configuration"""
    model_name: str = Field(default="gemini-pro")
    max_tokens: int = Field(default=1000, ge=100, le=4000)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    timeout_seconds: int = Field(default=30, ge=5, le=120)
    rate_limit_per_minute: int = Field(default=60, ge=1, le=1000)


class YouTubeConfig(BaseModel):
    """YouTube service configuration"""
    api_key: str = Field(..., description="YouTube API key")
    max_results_per_search: int = Field(default=10, ge=1, le=50)
    cache_duration_hours: int = Field(default=24, ge=1, le=168)
    relevance_threshold: float = Field(default=0.5, ge=0.0, le=1.0)
