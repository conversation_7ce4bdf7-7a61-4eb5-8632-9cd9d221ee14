# LeetCode Helper Pro - AI Backend 🧠

An intelligent AI-powered backend for the LeetCode Helper Pro extension using Google Gemini APIs.

## ✨ Features

### 🤖 AI-Powered Analysis
- **Problem Analysis**: Deep understanding of LeetCode problems using Gemini AI
- **Intelligent Hints**: Progressive 3-level hint system that guides without giving away solutions
- **Approach Recommendations**: AI suggests optimal algorithms and data structures
- **Complexity Analysis**: Automatic time/space complexity analysis

### 🎥 YouTube Integration
- **Smart Video Search**: AI-curated YouTube solution videos
- **Relevance Scoring**: Videos ranked by relevance to the specific problem
- **Struggle Detection**: Automatically suggests videos when users need help

### 📊 Advanced Features
- **User Context Awareness**: Personalized hints based on user behavior
- **Multi-language Support**: Hints tailored to programming languages
- **Progress Tracking**: Analytics and learning insights
- **Caching System**: Optimized performance with intelligent caching

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Google Gemini API key
- YouTube Data API key (optional)

### Installation

1. **Clone and Setup**
   ```bash
   cd backend
   python start_server.py --install
   ```

2. **Configure API Keys**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Start the Server**
   ```bash
   python start_server.py
   ```

The server will start at `http://localhost:8000` with API documentation at `/docs`.

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Optional but recommended
YOUTUBE_API_KEY=your_youtube_api_key_here

# Server settings
DEBUG=true
HOST=localhost
PORT=8000

# AI settings
DEFAULT_AI_MODEL=gemini-pro
MAX_TOKENS=1000
TEMPERATURE=0.7
```

### Getting API Keys

#### Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GOOGLE_API_KEY`

#### YouTube Data API Key
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Add it to your `.env` file as `YOUTUBE_API_KEY`

## 📡 API Endpoints

### Core Endpoints

#### `POST /api/v1/hint`
Generate intelligent hints for problems
```json
{
  "problem_data": {
    "title": "Two Sum",
    "slug": "two-sum",
    "difficulty": "Easy",
    "description": "...",
    "category": "Array"
  },
  "level": "gentle",
  "user_context": {
    "language": "python",
    "hint_count": 1,
    "time_spent": 15
  }
}
```

#### `POST /api/v1/analyze`
Analyze problem and suggest approaches
```json
{
  "problem_data": {
    "title": "Two Sum",
    "slug": "two-sum",
    "difficulty": "Easy",
    "description": "..."
  }
}
```

#### `POST /api/v1/youtube`
Find relevant YouTube solution videos
```json
{
  "problem_data": {
    "title": "Two Sum",
    "slug": "two-sum",
    "difficulty": "Easy"
  },
  "max_results": 5
}
```

#### `POST /api/v1/struggle-detection`
Detect if user is struggling
```json
{
  "problem_data": {...},
  "user_context": {
    "hint_count": 3,
    "time_spent": 25
  }
}
```

### Utility Endpoints

- `GET /health` - Health check
- `GET /api/v1/stats` - Service statistics

## 🏗️ Architecture

```
backend/
├── app/
│   ├── api/                 # API routes
│   ├── core/               # Configuration and utilities
│   ├── models/             # Pydantic schemas
│   └── services/           # Business logic
│       ├── ai_service.py   # Gemini AI integration
│       ├── youtube_service.py # YouTube search
│       └── hint_service.py # Intelligent hint generation
├── main.py                 # FastAPI application
├── start_server.py         # Startup script
└── requirements.txt        # Dependencies
```

## 🧠 AI System Design

### Hint Generation Process
1. **Context Analysis**: Analyze user behavior and problem context
2. **Level Adjustment**: Automatically adjust hint difficulty based on struggle detection
3. **AI Generation**: Use Gemini to generate educational hints
4. **Post-processing**: Ensure hints don't contain direct code solutions
5. **Personalization**: Add language-specific tips and encouragement

### Struggle Detection Algorithm
- **Hint Frequency**: Multiple hint requests indicate struggle
- **Time Analysis**: Extended time on problem suggests difficulty
- **Pattern Recognition**: AI analyzes interaction patterns
- **Adaptive Response**: Automatically suggests YouTube videos when appropriate

## 🔌 Extension Integration

The backend integrates seamlessly with the LeetCode Helper Pro extension:

1. **Auto-Detection**: Extension detects problems and sends data to backend
2. **Real-time Hints**: AI generates contextual hints based on user behavior
3. **Smart Suggestions**: Backend suggests YouTube videos when users struggle
4. **Progress Tracking**: Analytics help users understand their learning patterns

## 🛠️ Development

### Running in Development Mode
```bash
python start_server.py --host 0.0.0.0 --port 8000
```

### Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

### Code Quality
```bash
# Format code
black .

# Lint code
flake8 .

# Type checking
mypy .
```

## 📊 Monitoring

### Health Checks
- `/health` endpoint provides service status
- Individual service health monitoring
- Performance metrics tracking

### Logging
- Structured logging with loguru
- Configurable log levels
- Optional file logging

## 🚨 Troubleshooting

### Common Issues

**Server won't start:**
- Check Python version (3.8+ required)
- Verify all dependencies are installed
- Check port availability

**AI features not working:**
- Verify `GOOGLE_API_KEY` is set correctly
- Check API key permissions and quotas
- Review server logs for errors

**YouTube search failing:**
- Verify `YOUTUBE_API_KEY` is set
- Check YouTube API quotas
- Ensure API is enabled in Google Cloud Console

### Debug Mode
```bash
DEBUG=true python start_server.py
```

## 📈 Performance

### Optimization Features
- **Request Caching**: Intelligent caching of AI responses
- **Rate Limiting**: Prevents API quota exhaustion
- **Async Processing**: Non-blocking request handling
- **Connection Pooling**: Efficient API connections

### Scaling Considerations
- Stateless design for horizontal scaling
- Database support for persistent storage
- Redis integration for distributed caching

## 🔒 Security

- **API Key Protection**: Secure handling of sensitive credentials
- **CORS Configuration**: Proper cross-origin request handling
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: Protection against abuse

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review server logs
- Open an issue on GitHub

---

**Happy Coding with AI! 🚀**

*LeetCode Helper Pro - Your intelligent coding companion powered by Google Gemini*
