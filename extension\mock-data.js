// Mock Data System for LeetCode Helper Pro Testing
class MockDataProvider {
    constructor() {
        this.mockProblems = this.initializeMockProblems();
        this.mockHints = this.initializeMockHints();
        this.mockTestCases = this.initializeMockTestCases();
        this.mockApproaches = this.initializeMockApproaches();
        this.mockUserProgress = this.initializeMockUserProgress();
    }

    // Get mock problem data for testing
    getMockProblem(slug = 'two-sum') {
        return this.mockProblems[slug] || this.mockProblems['two-sum'];
    }

    // Get all mock problems
    getAllMockProblems() {
        return Object.values(this.mockProblems);
    }

    // Get mock hints for a problem
    getMockHints(problemSlug, level = 'gentle') {
        const problemHints = this.mockHints[problemSlug];
        if (problemHints && problemHints[level]) {
            return problemHints[level];
        }
        return this.mockHints['default'][level];
    }

    // Get mock test cases
    getMockTestCases(problemSlug) {
        return this.mockTestCases[problemSlug] || this.mockTestCases['default'];
    }

    // Get mock approach analysis
    getMockApproach(problemSlug) {
        return this.mockApproaches[problemSlug] || this.mockApproaches['default'];
    }

    // Get mock user progress data
    getMockUserProgress() {
        return { ...this.mockUserProgress };
    }

    // Initialize mock problems database
    initializeMockProblems() {
        return {
            'two-sum': {
                title: 'Two Sum',
                slug: 'two-sum',
                difficulty: 'Easy',
                category: 'Array',
                acceptance: '49.1%',
                description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
                url: 'https://leetcode.com/problems/two-sum/',
                tags: ['Array', 'Hash Table'],
                companies: ['Amazon', 'Google', 'Microsoft'],
                constraints: [
                    '2 <= nums.length <= 10^4',
                    '-10^9 <= nums[i] <= 10^9',
                    '-10^9 <= target <= 10^9',
                    'Only one valid answer exists'
                ]
            },
            'add-two-numbers': {
                title: 'Add Two Numbers',
                slug: 'add-two-numbers',
                difficulty: 'Medium',
                category: 'Linked List',
                acceptance: '37.8%',
                description: 'You are given two non-empty linked lists representing two non-negative integers.',
                url: 'https://leetcode.com/problems/add-two-numbers/',
                tags: ['Linked List', 'Math', 'Recursion'],
                companies: ['Amazon', 'Microsoft', 'Apple'],
                constraints: [
                    'The number of nodes in each linked list is in the range [1, 100]',
                    '0 <= Node.val <= 9'
                ]
            },
            'longest-substring-without-repeating-characters': {
                title: 'Longest Substring Without Repeating Characters',
                slug: 'longest-substring-without-repeating-characters',
                difficulty: 'Medium',
                category: 'String',
                acceptance: '33.8%',
                description: 'Given a string s, find the length of the longest substring without repeating characters.',
                url: 'https://leetcode.com/problems/longest-substring-without-repeating-characters/',
                tags: ['Hash Table', 'String', 'Sliding Window'],
                companies: ['Amazon', 'Bloomberg', 'Adobe'],
                constraints: [
                    '0 <= s.length <= 5 * 10^4',
                    's consists of English letters, digits, symbols and spaces'
                ]
            },
            'valid-parentheses': {
                title: 'Valid Parentheses',
                slug: 'valid-parentheses',
                difficulty: 'Easy',
                category: 'String',
                acceptance: '40.1%',
                description: 'Given a string s containing just the characters \'(\', \')\', \'{\', \'}\', \'[\' and \']\', determine if the input string is valid.',
                url: 'https://leetcode.com/problems/valid-parentheses/',
                tags: ['String', 'Stack'],
                companies: ['Amazon', 'Microsoft', 'Facebook'],
                constraints: [
                    '1 <= s.length <= 10^4',
                    's consists of parentheses only \'()[]{}\''
                ]
            },
            'merge-two-sorted-lists': {
                title: 'Merge Two Sorted Lists',
                slug: 'merge-two-sorted-lists',
                difficulty: 'Easy',
                category: 'Linked List',
                acceptance: '60.4%',
                description: 'You are given the heads of two sorted linked lists list1 and list2.',
                url: 'https://leetcode.com/problems/merge-two-sorted-lists/',
                tags: ['Linked List', 'Recursion'],
                companies: ['Amazon', 'Microsoft', 'Apple'],
                constraints: [
                    'The number of nodes in both lists is in the range [0, 50]',
                    '-100 <= Node.val <= 100'
                ]
            }
        };
    }

    // Initialize mock hints database
    initializeMockHints() {
        return {
            'two-sum': {
                gentle: 'Think about what data structure allows fast lookups of previously seen values. Consider the relationship between the current number and the target.',
                moderate: 'Use a hash map to store numbers you\'ve seen along with their indices. For each new number, check if its complement (target - current) exists in the map.',
                strong: 'Create a hash map. Iterate through the array once. For each number, calculate complement = target - number. If complement exists in map, return [map[complement], currentIndex]. Otherwise, add current number and index to map.'
            },
            'add-two-numbers': {
                gentle: 'Think about how you would add two numbers digit by digit on paper, handling carries as you go.',
                moderate: 'Process both linked lists simultaneously, adding corresponding digits plus any carry from the previous addition. Create a new linked list for the result.',
                strong: 'Use a dummy head node. Maintain a carry variable. While either list has nodes or carry exists: sum = val1 + val2 + carry, create new node with sum % 10, carry = sum // 10, advance pointers.'
            },
            'longest-substring-without-repeating-characters': {
                gentle: 'Consider keeping track of characters you\'ve seen in the current substring. What happens when you encounter a duplicate character?',
                moderate: 'Use a sliding window approach with a set to track unique characters. When you find a duplicate, shrink the window from the left until the duplicate is removed.',
                strong: 'Use two pointers (left, right) and a Set. Expand right pointer, add characters to set. When duplicate found, remove characters from left until duplicate is gone. Track maximum window size throughout.'
            },
            'valid-parentheses': {
                gentle: 'Think about the Last In, First Out (LIFO) nature of matching parentheses. What data structure follows this pattern?',
                moderate: 'Use a stack to keep track of opening brackets. When you encounter a closing bracket, check if it matches the most recent opening bracket.',
                strong: 'Use a stack and a mapping of bracket pairs. For each character: if opening bracket, push to stack; if closing bracket, check if stack is empty or top doesn\'t match - return false; finally, return stack.isEmpty().'
            },
            'merge-two-sorted-lists': {
                gentle: 'Since both lists are already sorted, you can compare elements from the front of each list and choose the smaller one.',
                moderate: 'Use two pointers, one for each list. Compare current nodes and add the smaller one to your result list. Advance the pointer of the list you took from.',
                strong: 'Create a dummy head node. Use a pointer to build result. While both lists have nodes: compare values, link smaller node to result, advance that list\'s pointer. After loop, link remaining nodes.'
            },
            'default': {
                gentle: 'Consider the problem constraints and think about what data structures might be helpful for this type of problem.',
                moderate: 'Break this problem down into smaller steps and consider the time and space complexity of different approaches.',
                strong: 'Think about the most efficient algorithm for this problem type and implement it step by step, handling edge cases carefully.'
            }
        };
    }

    // Initialize mock test cases
    initializeMockTestCases() {
        return {
            'two-sum': {
                basic: [
                    {
                        name: 'Basic Two Sum',
                        input: 'nums = [2,7,11,15], target = 9',
                        output: '[0,1]',
                        explanation: 'Because nums[0] + nums[1] = 2 + 7 = 9'
                    },
                    {
                        name: 'Different Positions',
                        input: 'nums = [3,2,4], target = 6',
                        output: '[1,2]',
                        explanation: 'Because nums[1] + nums[2] = 2 + 4 = 6'
                    }
                ],
                edge: [
                    {
                        name: 'Same Number Twice',
                        input: 'nums = [3,3], target = 6',
                        output: '[0,1]',
                        explanation: 'Uses same value from different indices'
                    }
                ],
                performance: [
                    {
                        name: 'Large Array',
                        input: 'nums = [1,2,3,...,10000], target = 19999',
                        output: '[9998,9999]',
                        explanation: 'Tests performance with maximum input size'
                    }
                ]
            },
            'default': {
                basic: [
                    {
                        name: 'Basic Test Case',
                        input: 'Standard input format',
                        output: 'Expected output',
                        explanation: 'Basic functionality test'
                    }
                ],
                edge: [
                    {
                        name: 'Edge Case',
                        input: 'Edge case input',
                        output: 'Edge case output',
                        explanation: 'Tests boundary conditions'
                    }
                ],
                performance: [
                    {
                        name: 'Performance Test',
                        input: 'Large input size',
                        output: 'Expected output for large input',
                        explanation: 'Tests algorithm efficiency'
                    }
                ]
            }
        };
    }

    // Initialize mock approach analysis
    initializeMockApproaches() {
        return {
            'two-sum': {
                primaryApproach: {
                    name: 'Hash Map Lookup',
                    algorithm: 'Hash Table',
                    timeComplexity: 'O(n)',
                    spaceComplexity: 'O(n)',
                    description: 'Use hash map to store complements and find pairs in single pass'
                },
                alternativeApproaches: [
                    {
                        name: 'Brute Force',
                        algorithm: 'Nested Loops',
                        timeComplexity: 'O(n²)',
                        spaceComplexity: 'O(1)',
                        description: 'Check all pairs using nested loops',
                        pros: 'Simple to implement, no extra space',
                        cons: 'Inefficient for large inputs'
                    },
                    {
                        name: 'Two Pointers (sorted)',
                        algorithm: 'Two Pointers',
                        timeComplexity: 'O(n log n)',
                        spaceComplexity: 'O(1)',
                        description: 'Sort array first, then use two pointers',
                        pros: 'Space efficient',
                        cons: 'Requires sorting, loses original indices'
                    }
                ],
                keyInsights: [
                    'Trade space for time efficiency',
                    'Hash maps provide O(1) average lookup time',
                    'Single pass solution is possible',
                    'Consider hash collision scenarios'
                ],
                implementationTips: [
                    'Check if complement exists before adding current number',
                    'Store both value and index in hash map',
                    'Handle duplicate values correctly'
                ],
                commonPitfalls: [
                    'Using same element twice',
                    'Not handling duplicate values',
                    'Forgetting to return indices instead of values'
                ]
            },
            'default': {
                primaryApproach: {
                    name: 'Standard Approach',
                    algorithm: 'General Algorithm',
                    timeComplexity: 'O(n)',
                    spaceComplexity: 'O(1)',
                    description: 'Standard approach for this problem type'
                },
                alternativeApproaches: [
                    {
                        name: 'Alternative Method',
                        algorithm: 'Alternative Algorithm',
                        timeComplexity: 'O(n log n)',
                        spaceComplexity: 'O(n)',
                        description: 'Alternative solution approach'
                    }
                ],
                keyInsights: [
                    'Consider time vs space tradeoffs',
                    'Think about edge cases',
                    'Optimize for the common case'
                ],
                implementationTips: [
                    'Use appropriate data structures',
                    'Handle edge cases first',
                    'Write clean, readable code'
                ],
                commonPitfalls: [
                    'Off-by-one errors',
                    'Not handling edge cases',
                    'Inefficient algorithm choice'
                ]
            }
        };
    }

    // Initialize mock user progress data
    initializeMockUserProgress() {
        return {
            problemsSolved: 42,
            hintsUsed: 28,
            totalTimeSpent: 1800000, // 30 hours in milliseconds
            streakDays: 7,
            lastActiveDate: new Date().toISOString(),
            difficultyBreakdown: {
                easy: 25,
                medium: 15,
                hard: 2
            },
            categoryBreakdown: {
                'Array': 15,
                'String': 10,
                'Linked List': 8,
                'Tree': 5,
                'Graph': 3,
                'Dynamic Programming': 1
            },
            languageUsage: {
                'javascript': 20,
                'python': 15,
                'java': 7
            },
            badges: [
                {
                    id: 'first_solve',
                    name: 'First Steps',
                    description: 'Solved your first problem',
                    icon: '🎯',
                    earnedDate: '2024-01-15T10:30:00Z'
                },
                {
                    id: 'problem_solver',
                    name: 'Problem Solver',
                    description: 'Solved 10 problems',
                    icon: '🧩',
                    earnedDate: '2024-01-20T14:15:00Z'
                },
                {
                    id: 'streak_starter',
                    name: 'Streak Starter',
                    description: 'Maintained a 3-day solving streak',
                    icon: '🔥',
                    earnedDate: '2024-01-25T09:45:00Z'
                }
            ],
            achievements: [
                {
                    id: 'category_master',
                    name: 'Array Master',
                    description: 'Solved 10 array problems',
                    points: 100,
                    earnedDate: '2024-01-28T16:20:00Z'
                }
            ],
            weeklyGoals: {
                problemsTarget: 5,
                problemsCompleted: 3,
                weekStart: new Date().toISOString()
            }
        };
    }

    // Generate random mock data for testing
    generateRandomProblem() {
        const difficulties = ['Easy', 'Medium', 'Hard'];
        const categories = ['Array', 'String', 'Linked List', 'Tree', 'Graph', 'Dynamic Programming'];
        const companies = ['Amazon', 'Google', 'Microsoft', 'Facebook', 'Apple', 'Netflix'];

        const randomTitle = `Random Problem ${Math.floor(Math.random() * 1000)}`;
        const randomSlug = randomTitle.toLowerCase().replace(/\s+/g, '-');

        return {
            title: randomTitle,
            slug: randomSlug,
            difficulty: difficulties[Math.floor(Math.random() * difficulties.length)],
            category: categories[Math.floor(Math.random() * categories.length)],
            acceptance: `${Math.floor(Math.random() * 60) + 20}%`,
            description: `This is a randomly generated problem for testing purposes.`,
            url: `https://leetcode.com/problems/${randomSlug}/`,
            tags: categories.slice(0, Math.floor(Math.random() * 3) + 1),
            companies: companies.slice(0, Math.floor(Math.random() * 3) + 1)
        };
    }

    // Simulate API delay for realistic testing
    async simulateApiCall(data, delay = 500) {
        return new Promise(resolve => {
            setTimeout(() => resolve(data), delay);
        });
    }

    // Get mock data for specific testing scenarios
    getTestingScenario(scenario) {
        const scenarios = {
            'new-user': {
                problem: this.getMockProblem('two-sum'),
                progress: {
                    problemsSolved: 0,
                    hintsUsed: 0,
                    badges: [],
                    achievements: []
                }
            },
            'experienced-user': {
                problem: this.getMockProblem('longest-substring-without-repeating-characters'),
                progress: this.getMockUserProgress()
            },
            'hard-problem': {
                problem: {
                    ...this.getMockProblem('two-sum'),
                    difficulty: 'Hard',
                    acceptance: '25.3%'
                },
                progress: this.getMockUserProgress()
            }
        };

        return scenarios[scenario] || scenarios['new-user'];
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MockDataProvider };
}

// Global instance for easy access
window.mockDataProvider = new MockDataProvider();
