// Popup JavaScript for LeetCode Helper Pro
class LeetCodeHelperPopup {
    constructor() {
        this.currentProblem = null;
        this.hintLevel = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadCurrentProblem();
        this.updateUI();
    }

    setupEventListeners() {
        // Hint buttons
        document.querySelectorAll('.hint-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleHintClick(e));
        });

        // Feature buttons
        document.getElementById('approachBtn').addEventListener('click', () => this.showApproachAnalysis());
        document.getElementById('testCaseBtn').addEventListener('click', () => this.showTestCases());
        document.getElementById('progressBtn').addEventListener('click', () => this.showProgress());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());

        // Modal controls
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') this.closeModal();
        });

        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'PROBLEM_DETECTED') {
                this.currentProblem = message.problem;
                this.updateUI();
            }
        });
    }

    async loadCurrentProblem() {
        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (tab.url && tab.url.includes('leetcode.com/problems/')) {
                // Send message to content script to get problem data
                chrome.tabs.sendMessage(tab.id, { type: 'GET_PROBLEM_DATA' }, (response) => {
                    if (response && response.problem) {
                        this.currentProblem = response.problem;
                        this.updateUI();
                    }
                });
            }
        } catch (error) {
            console.error('Error loading current problem:', error);
        }
    }

    updateUI() {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const problemTitle = document.getElementById('problemTitle');
        const difficultyBadge = document.getElementById('difficultyBadge');
        const difficultyText = document.getElementById('difficultyText');
        const problemCategory = document.getElementById('problemCategory');
        const problemAcceptance = document.getElementById('problemAcceptance');

        if (this.currentProblem) {
            statusText.textContent = 'Problem Detected';
            problemTitle.textContent = this.currentProblem.title;
            difficultyText.textContent = this.currentProblem.difficulty;
            difficultyBadge.className = `difficulty-badge ${this.currentProblem.difficulty.toLowerCase()}`;
            problemCategory.textContent = this.currentProblem.category || 'Unknown';
            problemAcceptance.textContent = this.currentProblem.acceptance || 'N/A';
        } else {
            statusText.textContent = 'No Problem';
            problemTitle.textContent = 'No Problem Detected';
            difficultyText.textContent = '-';
            difficultyBadge.className = 'difficulty-badge';
            problemCategory.textContent = '-';
            problemAcceptance.textContent = '-';
        }
    }

    async handleHintClick(event) {
        const button = event.currentTarget;
        const level = button.dataset.level;
        
        this.showLoading();
        
        try {
            const hint = await this.generateHint(level);
            this.displayHint(hint, level);
        } catch (error) {
            console.error('Error generating hint:', error);
            this.displayHint('Sorry, unable to generate hint at this time.', level);
        } finally {
            this.hideLoading();
        }
    }

    async generateHint(level) {
        if (!this.currentProblem) {
            return 'Please navigate to a LeetCode problem first.';
        }

        // Get hint from background script or mock data
        return new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'GENERATE_HINT',
                problem: this.currentProblem,
                level: level
            }, (response) => {
                resolve(response?.hint || this.getMockHint(level));
            });
        });
    }

    getMockHint(level) {
        const hints = {
            gentle: {
                'Two Sum': 'Think about what data structure allows fast lookups...',
                'Add Two Numbers': 'Consider how you add numbers digit by digit...',
                'default': 'Consider the problem constraints and think about efficient data structures.'
            },
            moderate: {
                'Two Sum': 'Use a hash map to store numbers you\'ve seen and their indices.',
                'Add Two Numbers': 'Process both linked lists simultaneously, handling carry values.',
                'default': 'Break down the problem into smaller subproblems and consider optimal approaches.'
            },
            strong: {
                'Two Sum': 'Iterate through the array, for each number check if (target - number) exists in hash map.',
                'Add Two Numbers': 'Create a new linked list, add corresponding digits plus carry, handle different lengths.',
                'default': 'Here\'s a detailed step-by-step approach to solve this problem efficiently.'
            }
        };

        const problemTitle = this.currentProblem?.title || 'default';
        return hints[level][problemTitle] || hints[level]['default'];
    }

    displayHint(hint, level) {
        const hintDisplay = document.getElementById('hintDisplay');
        const levelColors = {
            gentle: '#4facfe',
            moderate: '#43e97b',
            strong: '#fa709a'
        };

        hintDisplay.innerHTML = `
            <div style="border-left: 3px solid ${levelColors[level]}; padding-left: 12px;">
                <div style="font-weight: 500; margin-bottom: 8px; text-transform: capitalize;">
                    ${level} Hint
                </div>
                <div style="color: var(--text-secondary); line-height: 1.4;">
                    ${hint}
                </div>
            </div>
        `;
    }

    showApproachAnalysis() {
        if (!this.currentProblem) {
            this.showModal('Approach Analysis', 'Please navigate to a LeetCode problem first.');
            return;
        }

        const content = `
            <div style="space-y: 16px;">
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">Recommended Approach</h4>
                    <p style="color: var(--text-secondary);">Hash Map / Two Pointer Technique</p>
                </div>
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">Time Complexity</h4>
                    <p style="color: var(--text-secondary);">O(n) - Single pass through the array</p>
                </div>
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">Space Complexity</h4>
                    <p style="color: var(--text-secondary);">O(n) - Hash map storage</p>
                </div>
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">Key Insights</h4>
                    <ul style="color: var(--text-secondary); padding-left: 20px;">
                        <li>Trade space for time efficiency</li>
                        <li>Avoid nested loops with smart data structures</li>
                        <li>Consider edge cases and constraints</li>
                    </ul>
                </div>
            </div>
        `;

        this.showModal('Approach Analysis', content);
    }

    showTestCases() {
        if (!this.currentProblem) {
            this.showModal('Test Cases', 'Please navigate to a LeetCode problem first.');
            return;
        }

        const content = `
            <div style="space-y: 12px;">
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Basic Test Case:</strong><br>
                    Input: [2,7,11,15], target = 9<br>
                    Output: [0,1]
                </div>
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Edge Case:</strong><br>
                    Input: [3,3], target = 6<br>
                    Output: [0,1]
                </div>
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Large Numbers:</strong><br>
                    Input: [1000000, 2000000], target = 3000000<br>
                    Output: [0,1]
                </div>
            </div>
        `;

        this.showModal('Generated Test Cases', content);
    }

    showProgress() {
        const content = `
            <div style="text-align: center;">
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 48px; margin-bottom: 8px;">🏆</div>
                    <h4 style="color: var(--text-primary);">Progress Overview</h4>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                    <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #4facfe;">42</div>
                        <div style="color: var(--text-secondary); font-size: 12px;">Problems Solved</div>
                    </div>
                    <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #43e97b;">15</div>
                        <div style="color: var(--text-secondary); font-size: 12px;">Hints Used</div>
                    </div>
                </div>
                <div style="text-align: left;">
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Recent Badges</h5>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: var(--success-gradient); padding: 4px 8px; border-radius: 12px; font-size: 12px;">First Solve</span>
                        <span style="background: var(--warning-gradient); padding: 4px 8px; border-radius: 12px; font-size: 12px;">Speed Demon</span>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Your Progress', content);
    }

    showSettings() {
        const content = `
            <div style="space-y: 16px;">
                <div>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" checked style="margin-right: 8px;"> Auto-detect problems
                    </label>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" checked style="margin-right: 8px;"> Show context menu
                    </label>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" style="margin-right: 8px;"> Dark mode
                    </label>
                </div>
                <div>
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Preferred Language</h5>
                    <select style="width: 100%; padding: 8px; background: var(--bg-tertiary); color: var(--text-primary); border: 1px solid rgba(255,255,255,0.1); border-radius: 4px;">
                        <option>JavaScript</option>
                        <option>Python</option>
                        <option>Java</option>
                        <option>C++</option>
                    </select>
                </div>
                <div>
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Hint Preference</h5>
                    <select style="width: 100%; padding: 8px; background: var(--bg-tertiary); color: var(--text-primary); border: 1px solid rgba(255,255,255,0.1); border-radius: 4px;">
                        <option>Progressive (Gentle → Strong)</option>
                        <option>Direct (Skip to Strong)</option>
                        <option>Minimal (Gentle only)</option>
                    </select>
                </div>
            </div>
        `;

        this.showModal('Settings', content);
    }

    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.classList.add('active');
    }

    closeModal() {
        document.getElementById('modal').classList.remove('active');
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LeetCodeHelperPopup();
});
