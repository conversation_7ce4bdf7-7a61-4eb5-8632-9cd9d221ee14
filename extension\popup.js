// Popup JavaScript for LeetCode Helper Pro
class LeetCodeHelperPopup {
    constructor() {
        this.currentProblem = null;
        this.hintLevel = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadCurrentProblem();
        this.updateUI();
        this.startPeriodicStruggleDetection();
    }

    setupEventListeners() {
        // Hint buttons
        document.querySelectorAll('.hint-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleHintClick(e));
        });

        // Feature buttons
        document.getElementById('approachBtn').addEventListener('click', () => this.showApproachAnalysis());
        document.getElementById('testCaseBtn').addEventListener('click', () => this.showTestCases());
        document.getElementById('progressBtn').addEventListener('click', () => this.showProgress());
        document.getElementById('settingsBtn').addEventListener('click', () => this.showSettings());

        // Modal controls
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') this.closeModal();
        });

        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'PROBLEM_DETECTED') {
                // Check if this is a new problem
                const isNewProblem = !this.currentProblem ||
                    this.currentProblem.slug !== message.problem.slug;

                this.currentProblem = message.problem;

                // Start timer for new problems
                if (isNewProblem) {
                    this.resetProblemSession();
                    this.startProblemTimer();
                }

                this.updateUI();
            }
        });
    }

    async loadCurrentProblem() {
        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (tab.url && tab.url.includes('leetcode.com/problems/')) {
                // Send message to content script to get problem data
                chrome.tabs.sendMessage(tab.id, { type: 'GET_PROBLEM_DATA' }, (response) => {
                    if (response && response.problem) {
                        this.currentProblem = response.problem;
                        this.updateUI();
                    }
                });
            }
        } catch (error) {
            console.error('Error loading current problem:', error);
        }
    }

    updateUI() {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const problemTitle = document.getElementById('problemTitle');
        const difficultyBadge = document.getElementById('difficultyBadge');
        const difficultyText = document.getElementById('difficultyText');
        const problemCategory = document.getElementById('problemCategory');
        const problemAcceptance = document.getElementById('problemAcceptance');

        if (this.currentProblem) {
            statusText.textContent = 'Problem Detected';
            problemTitle.textContent = this.currentProblem.title;
            difficultyText.textContent = this.currentProblem.difficulty;
            difficultyBadge.className = `difficulty-badge ${this.currentProblem.difficulty.toLowerCase()}`;
            problemCategory.textContent = this.currentProblem.category || 'Unknown';
            problemAcceptance.textContent = this.currentProblem.acceptance || 'N/A';
        } else {
            statusText.textContent = 'No Problem';
            problemTitle.textContent = 'No Problem Detected';
            difficultyText.textContent = '-';
            difficultyBadge.className = 'difficulty-badge';
            problemCategory.textContent = '-';
            problemAcceptance.textContent = '-';
        }
    }

    async handleHintClick(event) {
        const button = event.currentTarget;
        const level = button.dataset.level;
        
        this.showLoading();
        
        try {
            const hint = await this.generateHint(level);
            this.displayHint(hint, level);
        } catch (error) {
            console.error('Error generating hint:', error);
            this.displayHint('Sorry, unable to generate hint at this time.', level);
        } finally {
            this.hideLoading();
        }
    }

    async generateHint(level) {
        if (!this.currentProblem) {
            return 'Please navigate to a LeetCode problem first.';
        }

        // Increment hint count
        this.incrementHintCount();

        // Get user context for better hints
        const userContext = {
            language: this.getUserLanguage(),
            timeSpent: this.getTimeSpent(),
            hintCount: this.getHintCount(),
            previousHints: this.getPreviousHints(),
            struggleIndicators: this.getStruggleIndicators()
        };

        // Get hint from background script with AI integration
        const hint = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'GENERATE_AI_HINT',
                problem: this.currentProblem,
                level: level,
                userContext: userContext
            }, (response) => {
                if (response?.success) {
                    resolve(response.hint);
                } else {
                    resolve(this.getMockHint(level));
                }
            });
        });

        // Add to previous hints
        this.addToPreviousHints(hint);

        // Check for struggle after hint generation
        setTimeout(() => this.checkForStruggle(), 1000);

        return hint;
    }

    getMockHint(level) {
        const hints = {
            gentle: {
                'Two Sum': 'Think about what data structure allows fast lookups...',
                'Add Two Numbers': 'Consider how you add numbers digit by digit...',
                'default': 'Consider the problem constraints and think about efficient data structures.'
            },
            moderate: {
                'Two Sum': 'Use a hash map to store numbers you\'ve seen and their indices.',
                'Add Two Numbers': 'Process both linked lists simultaneously, handling carry values.',
                'default': 'Break down the problem into smaller subproblems and consider optimal approaches.'
            },
            strong: {
                'Two Sum': 'Iterate through the array, for each number check if (target - number) exists in hash map.',
                'Add Two Numbers': 'Create a new linked list, add corresponding digits plus carry, handle different lengths.',
                'default': 'Here\'s a detailed step-by-step approach to solve this problem efficiently.'
            }
        };

        const problemTitle = this.currentProblem?.title || 'default';
        return hints[level][problemTitle] || hints[level]['default'];
    }

    displayHint(hint, level) {
        const hintDisplay = document.getElementById('hintDisplay');
        const levelColors = {
            gentle: '#4facfe',
            moderate: '#43e97b',
            strong: '#fa709a'
        };

        hintDisplay.innerHTML = `
            <div style="border-left: 3px solid ${levelColors[level]}; padding-left: 12px;">
                <div style="font-weight: 500; margin-bottom: 8px; text-transform: capitalize;">
                    ${level} Hint
                </div>
                <div style="color: var(--text-secondary); line-height: 1.4;">
                    ${hint}
                </div>
            </div>
        `;
    }

    async showApproachAnalysis() {
        if (!this.currentProblem) {
            this.showModal('Approach Analysis', 'Please navigate to a LeetCode problem first.');
            return;
        }

        this.showLoading();

        try {
            // Get AI-powered analysis
            const analysis = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    type: 'ANALYZE_PROBLEM',
                    problem: this.currentProblem
                }, (response) => {
                    resolve(response);
                });
            });

            let content;
            if (analysis?.success) {
                content = `
                    <div style="space-y: 16px;">
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Problem Type</h4>
                            <p style="color: var(--text-secondary);">${analysis.problemType}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Recommended Approach</h4>
                            <p style="color: var(--text-secondary);">${analysis.optimalApproach?.name || 'Standard Approach'}</p>
                            <p style="color: var(--text-muted); font-size: 12px; margin-top: 4px;">${analysis.optimalApproach?.description || ''}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Time Complexity</h4>
                            <p style="color: var(--text-secondary);">${analysis.optimalApproach?.time_complexity || 'O(n)'}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Space Complexity</h4>
                            <p style="color: var(--text-secondary);">${analysis.optimalApproach?.space_complexity || 'O(1)'}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Key Insights</h4>
                            <ul style="color: var(--text-secondary); padding-left: 20px;">
                                ${(analysis.keyInsights || []).map(insight => `<li>${insight}</li>`).join('')}
                            </ul>
                        </div>
                        ${analysis.source === 'ai' ? '<div style="font-size: 10px; color: var(--text-muted); margin-top: 16px;">✨ AI-Generated Analysis</div>' : ''}
                    </div>
                `;
            } else {
                content = `
                    <div style="space-y: 16px;">
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Recommended Approach</h4>
                            <p style="color: var(--text-secondary);">Hash Map / Two Pointer Technique</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Time Complexity</h4>
                            <p style="color: var(--text-secondary);">O(n) - Single pass through the data</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Space Complexity</h4>
                            <p style="color: var(--text-secondary);">O(n) - Additional storage</p>
                        </div>
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">Key Insights</h4>
                            <ul style="color: var(--text-secondary); padding-left: 20px;">
                                <li>Trade space for time efficiency</li>
                                <li>Avoid nested loops with smart data structures</li>
                                <li>Consider edge cases and constraints</li>
                            </ul>
                        </div>
                    </div>
                `;
            }

            this.hideLoading();
            this.showModal('Approach Analysis', content);

        } catch (error) {
            console.error('Error getting approach analysis:', error);
            this.hideLoading();
            this.showModal('Approach Analysis', 'Analysis temporarily unavailable. Please try again.');
        }
    }

    showTestCases() {
        if (!this.currentProblem) {
            this.showModal('Test Cases', 'Please navigate to a LeetCode problem first.');
            return;
        }

        const content = `
            <div style="space-y: 12px;">
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Basic Test Case:</strong><br>
                    Input: [2,7,11,15], target = 9<br>
                    Output: [0,1]
                </div>
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Edge Case:</strong><br>
                    Input: [3,3], target = 6<br>
                    Output: [0,1]
                </div>
                <div style="background: var(--bg-tertiary); padding: 12px; border-radius: 8px; margin-bottom: 12px;">
                    <strong>Large Numbers:</strong><br>
                    Input: [1000000, 2000000], target = 3000000<br>
                    Output: [0,1]
                </div>
            </div>
        `;

        this.showModal('Generated Test Cases', content);
    }

    showProgress() {
        const content = `
            <div style="text-align: center;">
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 48px; margin-bottom: 8px;">🏆</div>
                    <h4 style="color: var(--text-primary);">Progress Overview</h4>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                    <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #4facfe;">42</div>
                        <div style="color: var(--text-secondary); font-size: 12px;">Problems Solved</div>
                    </div>
                    <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px;">
                        <div style="font-size: 24px; font-weight: bold; color: #43e97b;">15</div>
                        <div style="color: var(--text-secondary); font-size: 12px;">Hints Used</div>
                    </div>
                </div>
                <div style="text-align: left;">
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Recent Badges</h5>
                    <div style="display: flex; gap: 8px;">
                        <span style="background: var(--success-gradient); padding: 4px 8px; border-radius: 12px; font-size: 12px;">First Solve</span>
                        <span style="background: var(--warning-gradient); padding: 4px 8px; border-radius: 12px; font-size: 12px;">Speed Demon</span>
                    </div>
                </div>
            </div>
        `;

        this.showModal('Your Progress', content);
    }

    showSettings() {
        const content = `
            <div style="space-y: 16px;">
                <div>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" checked style="margin-right: 8px;"> Auto-detect problems
                    </label>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" checked style="margin-right: 8px;"> Show context menu
                    </label>
                    <label style="display: block; margin-bottom: 8px; color: var(--text-primary);">
                        <input type="checkbox" style="margin-right: 8px;"> Dark mode
                    </label>
                </div>
                <div>
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Preferred Language</h5>
                    <select style="width: 100%; padding: 8px; background: var(--bg-tertiary); color: var(--text-primary); border: 1px solid rgba(255,255,255,0.1); border-radius: 4px;">
                        <option>JavaScript</option>
                        <option>Python</option>
                        <option>Java</option>
                        <option>C++</option>
                    </select>
                </div>
                <div>
                    <h5 style="color: var(--text-primary); margin-bottom: 8px;">Hint Preference</h5>
                    <select style="width: 100%; padding: 8px; background: var(--bg-tertiary); color: var(--text-primary); border: 1px solid rgba(255,255,255,0.1); border-radius: 4px;">
                        <option>Progressive (Gentle → Strong)</option>
                        <option>Direct (Skip to Strong)</option>
                        <option>Minimal (Gentle only)</option>
                    </select>
                </div>
            </div>
        `;

        this.showModal('Settings', content);
    }

    showModal(title, content) {
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modal.classList.add('active');
    }

    closeModal() {
        document.getElementById('modal').classList.remove('active');
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    // Helper methods for user context
    getUserLanguage() {
        return localStorage.getItem('leetcode-helper-language') || 'javascript';
    }

    getTimeSpent() {
        const startTime = localStorage.getItem('leetcode-helper-start-time');
        if (startTime) {
            return Math.floor((Date.now() - parseInt(startTime)) / 60000); // minutes
        }
        return 0;
    }

    getHintCount() {
        return parseInt(localStorage.getItem('leetcode-helper-hint-count') || '0');
    }

    getPreviousHints() {
        const hints = localStorage.getItem('leetcode-helper-previous-hints');
        return hints ? JSON.parse(hints) : [];
    }

    incrementHintCount() {
        const count = this.getHintCount() + 1;
        localStorage.setItem('leetcode-helper-hint-count', count.toString());
    }

    addToPreviousHints(hint) {
        const hints = this.getPreviousHints();
        hints.push(hint);
        if (hints.length > 5) hints.shift(); // Keep only last 5 hints
        localStorage.setItem('leetcode-helper-previous-hints', JSON.stringify(hints));
    }

    getStruggleIndicators() {
        const indicators = [];

        // Check various struggle indicators
        if (this.getHintCount() >= 3) {
            indicators.push('multiple_hints');
        }

        if (this.getTimeSpent() > 20) {
            indicators.push('extended_time');
        }

        // Check if user has been inactive for a while then came back
        const lastActivity = localStorage.getItem('leetcode-helper-last-activity');
        if (lastActivity) {
            const timeSinceActivity = Date.now() - parseInt(lastActivity);
            if (timeSinceActivity > 300000) { // 5 minutes
                indicators.push('long_pause');
            }
        }

        // Update last activity
        localStorage.setItem('leetcode-helper-last-activity', Date.now().toString());

        return indicators;
    }

    startProblemTimer() {
        if (!localStorage.getItem('leetcode-helper-start-time')) {
            localStorage.setItem('leetcode-helper-start-time', Date.now().toString());
        }
    }

    resetProblemSession() {
        localStorage.removeItem('leetcode-helper-start-time');
        localStorage.removeItem('leetcode-helper-hint-count');
        localStorage.removeItem('leetcode-helper-previous-hints');
        localStorage.removeItem('leetcode-helper-last-activity');
    }

    async checkForStruggle() {
        if (!this.currentProblem) return;

        const userContext = {
            language: this.getUserLanguage(),
            timeSpent: this.getTimeSpent(),
            hintCount: this.getHintCount(),
            previousHints: this.getPreviousHints(),
            struggleIndicators: this.getStruggleIndicators(),
            interactionHistory: this.getInteractionHistory()
        };

        // Check if user is struggling
        chrome.runtime.sendMessage({
            type: 'DETECT_STRUGGLE',
            problem: this.currentProblem,
            userContext: userContext
        }, (response) => {
            if (response) {
                this.handleStruggleDetectionResult(response);
            }
        });
    }

    handleStruggleDetectionResult(result) {
        // Store struggle detection result
        localStorage.setItem('leetcode-helper-last-struggle-check', JSON.stringify({
            timestamp: Date.now(),
            result: result
        }));

        // Show appropriate intervention based on struggle level
        if (result.shouldSuggestYouTube) {
            this.showYouTubeSuggestion(result);
        } else if (result.isStruggling && result.struggleScore > 0.5) {
            this.showStruggleSupport(result);
        } else if (result.struggleScore < 0.3) {
            this.showEncouragement(result);
        }
    }

    showStruggleSupport(result) {
        // Show a subtle support message without being intrusive
        const supportMessage = document.createElement('div');
        supportMessage.className = 'struggle-support-message';
        supportMessage.innerHTML = `
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px;
                border-radius: 8px;
                margin: 8px 0;
                font-size: 12px;
                text-align: center;
                animation: fadeIn 0.3s ease;
            ">
                💡 ${result.encouragementMessage || "Take your time - you're learning!"}
                <div style="margin-top: 8px;">
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-size: 10px;
                        cursor: pointer;
                    ">Got it</button>
                </div>
            </div>
        `;

        // Insert after problem info
        const problemInfo = document.querySelector('.problem-info');
        if (problemInfo) {
            problemInfo.parentNode.insertBefore(supportMessage, problemInfo.nextSibling);

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (supportMessage.parentNode) {
                    supportMessage.remove();
                }
            }, 10000);
        }
    }

    showEncouragement(result) {
        // Show brief encouragement for users doing well
        if (Math.random() < 0.3) { // Only show 30% of the time to avoid spam
            const encouragement = document.createElement('div');
            encouragement.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    margin: 8px 0;
                    font-size: 11px;
                    text-align: center;
                    animation: fadeIn 0.3s ease;
                ">
                    ✨ ${result.encouragementMessage || "Great progress! Keep it up!"}
                </div>
            `;

            const problemInfo = document.querySelector('.problem-info');
            if (problemInfo) {
                problemInfo.parentNode.insertBefore(encouragement, problemInfo.nextSibling);

                setTimeout(() => {
                    if (encouragement.parentNode) {
                        encouragement.remove();
                    }
                }, 5000);
            }
        }
    }

    getInteractionHistory() {
        const history = localStorage.getItem('leetcode-helper-interaction-history');
        return history ? JSON.parse(history) : [];
    }

    addToInteractionHistory(type, data) {
        const history = this.getInteractionHistory();
        history.push({
            type: type,
            data: data,
            timestamp: new Date().toISOString()
        });

        // Keep only last 20 interactions
        if (history.length > 20) {
            history.splice(0, history.length - 20);
        }

        localStorage.setItem('leetcode-helper-interaction-history', JSON.stringify(history));
    }

    async showYouTubeSuggestion(struggleResult = null) {
        const struggleLevel = struggleResult?.struggleLevel || 'moderate';
        const indicators = struggleResult?.indicators || [];
        const recommendations = struggleResult?.recommendations || [];

        let message = "It looks like you might be struggling with this problem.";
        let icon = "🎥";

        // Customize message based on struggle level
        if (struggleLevel === 'severe') {
            message = "This problem seems quite challenging for you right now.";
            icon = "🆘";
        } else if (struggleLevel === 'moderate') {
            message = "You've been working hard on this problem.";
            icon = "💪";
        } else {
            message = "Would you like some video guidance?";
            icon = "🎯";
        }

        const content = `
            <div style="text-align: center; padding: 20px;">
                <div style="font-size: 48px; margin-bottom: 16px;">${icon}</div>
                <h4 style="color: var(--text-primary); margin-bottom: 12px;">Need Some Help?</h4>
                <p style="color: var(--text-secondary); margin-bottom: 16px;">
                    ${message} Would you like to watch some solution videos?
                </p>
                ${indicators.length > 0 ? `
                    <div style="background: rgba(255,255,255,0.05); padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <p style="color: var(--text-muted); font-size: 12px; margin-bottom: 8px;">Detected patterns:</p>
                        <p style="color: var(--text-secondary); font-size: 11px;">${indicators.slice(0, 2).join(', ')}</p>
                    </div>
                ` : ''}
                ${recommendations.length > 0 ? `
                    <div style="background: rgba(255,255,255,0.05); padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <p style="color: var(--text-muted); font-size: 12px; margin-bottom: 8px;">Suggestions:</p>
                        <p style="color: var(--text-secondary); font-size: 11px;">${recommendations[0]}</p>
                    </div>
                ` : ''}
                <button id="findVideosBtn" style="
                    background: var(--primary-gradient);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: 500;
                    margin-right: 12px;
                ">Find Videos</button>
                <button id="continueBtn" style="
                    background: transparent;
                    color: var(--text-secondary);
                    border: 1px solid var(--text-secondary);
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                ">Keep Trying</button>
            </div>
        `;

        this.showModal('YouTube Suggestion', content);

        // Track this intervention
        this.addToInteractionHistory('youtube_suggestion', {
            struggleLevel: struggleLevel,
            indicators: indicators,
            timestamp: Date.now()
        });

        // Add event listeners
        document.getElementById('findVideosBtn')?.addEventListener('click', () => {
            this.closeModal();
            this.findYouTubeVideos();
            this.addToInteractionHistory('youtube_accepted', { timestamp: Date.now() });
        });

        document.getElementById('continueBtn')?.addEventListener('click', () => {
            this.closeModal();
            this.addToInteractionHistory('youtube_declined', { timestamp: Date.now() });
        });
    }

    async findYouTubeVideos() {
        if (!this.currentProblem) return;

        this.showLoading();

        try {
            const videos = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    type: 'FIND_YOUTUBE_VIDEOS',
                    problem: this.currentProblem,
                    maxResults: 5
                }, (response) => {
                    resolve(response);
                });
            });

            let content;
            if (videos?.success && videos.videos?.length > 0) {
                content = `
                    <div style="max-height: 400px; overflow-y: auto;">
                        <p style="color: var(--text-secondary); margin-bottom: 16px; font-size: 14px;">
                            Here are some helpful solution videos for this problem:
                        </p>
                        ${videos.videos.map(video => `
                            <div style="
                                background: var(--bg-tertiary);
                                border-radius: 8px;
                                padding: 12px;
                                margin-bottom: 12px;
                                border: 1px solid rgba(255,255,255,0.05);
                            ">
                                <div style="display: flex; gap: 12px;">
                                    ${video.thumbnail ? `<img src="${video.thumbnail}" style="width: 80px; height: 60px; border-radius: 4px; object-fit: cover;">` : ''}
                                    <div style="flex: 1;">
                                        <h5 style="color: var(--text-primary); margin-bottom: 4px; font-size: 13px; line-height: 1.3;">
                                            ${video.title}
                                        </h5>
                                        <p style="color: var(--text-muted); font-size: 11px; margin-bottom: 8px;">
                                            ${video.channel} ${video.duration ? `• ${video.duration}` : ''}
                                        </p>
                                        <a href="${video.url}" target="_blank" style="
                                            color: #4facfe;
                                            text-decoration: none;
                                            font-size: 12px;
                                            font-weight: 500;
                                        ">Watch Video →</a>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                        ${videos.source === 'ai' ? '<div style="font-size: 10px; color: var(--text-muted); margin-top: 16px;">✨ AI-Curated Videos</div>' : ''}
                    </div>
                `;
            } else {
                content = `
                    <div style="text-align: center; padding: 20px;">
                        <p style="color: var(--text-secondary); margin-bottom: 16px;">
                            ${videos?.message || 'No videos found at the moment.'}
                        </p>
                        <p style="color: var(--text-muted); font-size: 12px;">
                            Try searching YouTube manually for: "${this.currentProblem.title} leetcode solution"
                        </p>
                    </div>
                `;
            }

            this.hideLoading();
            this.showModal('Solution Videos', content);

        } catch (error) {
            console.error('Error finding YouTube videos:', error);
            this.hideLoading();
            this.showModal('Solution Videos', 'Unable to find videos at the moment. Please try again later.');
        }
    }

    startPeriodicStruggleDetection() {
        // Check for struggle every 2 minutes
        this.struggleDetectionInterval = setInterval(() => {
            if (this.currentProblem && this.getTimeSpent() > 5) { // Only after 5 minutes
                this.checkForStruggle();
            }
        }, 120000); // 2 minutes

        // Also check after significant time milestones
        setTimeout(() => {
            if (this.currentProblem && this.getTimeSpent() > 10) {
                this.checkForStruggle();
            }
        }, 600000); // 10 minutes

        setTimeout(() => {
            if (this.currentProblem && this.getTimeSpent() > 20) {
                this.checkForStruggle();
            }
        }, 1200000); // 20 minutes
    }

    stopPeriodicStruggleDetection() {
        if (this.struggleDetectionInterval) {
            clearInterval(this.struggleDetectionInterval);
            this.struggleDetectionInterval = null;
        }
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LeetCodeHelperPopup();
});
