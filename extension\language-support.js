// Multi-Language Support System for LeetCode Helper Pro
class LanguageSupport {
    constructor() {
        this.supportedLanguages = this.initializeSupportedLanguages();
        this.syntaxHighlighter = new SyntaxHighlighter();
        this.codeTemplateGenerator = new CodeTemplateGenerator();
        this.languageSpecificHints = new LanguageSpecificHints();
    }

    detectCurrentLanguage() {
        // Try to detect language from LeetCode's language selector
        const languageSelectors = [
            '[data-cy="lang-select"]',
            '.ant-select-selection-item',
            '[class*="language-select"]',
            '.lang-select'
        ];

        for (const selector of languageSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent) {
                const detectedLang = this.normalizeLanguageName(element.textContent.trim());
                if (this.supportedLanguages[detectedLang]) {
                    return detectedLang;
                }
            }
        }

        // Fallback: detect from code editor content
        return this.detectFromCodeEditor();
    }

    detectFromCodeEditor() {
        const codeEditor = document.querySelector('.monaco-editor, .CodeMirror, [class*="code-editor"]');
        if (!codeEditor) return 'javascript'; // Default fallback

        const codeContent = codeEditor.textContent || '';
        
        // Language detection patterns
        const patterns = {
            'python': [/def\s+\w+/, /import\s+\w+/, /from\s+\w+\s+import/, /:\s*$/m],
            'java': [/public\s+class/, /public\s+static\s+void/, /System\.out\.println/],
            'cpp': [/#include\s*</, /std::/, /cout\s*<</, /int\s+main\s*\(/],
            'csharp': [/using\s+System/, /public\s+class/, /Console\.WriteLine/],
            'javascript': [/function\s+\w+/, /const\s+\w+\s*=/, /let\s+\w+\s*=/, /=>\s*{/],
            'typescript': [/interface\s+\w+/, /type\s+\w+\s*=/, /:\s*string/, /:\s*number/],
            'go': [/package\s+main/, /func\s+\w+/, /fmt\.Println/],
            'rust': [/fn\s+\w+/, /let\s+mut/, /println!/, /impl\s+\w+/],
            'swift': [/func\s+\w+/, /var\s+\w+/, /let\s+\w+/, /print\(/],
            'kotlin': [/fun\s+\w+/, /val\s+\w+/, /var\s+\w+/, /println\(/]
        };

        for (const [lang, langPatterns] of Object.entries(patterns)) {
            if (langPatterns.some(pattern => pattern.test(codeContent))) {
                return lang;
            }
        }

        return 'javascript'; // Default fallback
    }

    normalizeLanguageName(langName) {
        const normalizations = {
            'JavaScript': 'javascript',
            'Python': 'python',
            'Python3': 'python',
            'Java': 'java',
            'C++': 'cpp',
            'C#': 'csharp',
            'TypeScript': 'typescript',
            'Go': 'go',
            'Rust': 'rust',
            'Swift': 'swift',
            'Kotlin': 'kotlin',
            'Ruby': 'ruby',
            'PHP': 'php',
            'Scala': 'scala'
        };

        return normalizations[langName] || langName.toLowerCase();
    }

    getLanguageInfo(language) {
        return this.supportedLanguages[language] || this.supportedLanguages['javascript'];
    }

    generateLanguageSpecificHint(problem, level, language) {
        const baseHint = this.getBaseHint(problem, level);
        const languageHint = this.languageSpecificHints.getHint(problem, level, language);
        const codeExample = this.generateCodeExample(problem, language, level);

        return {
            baseHint,
            languageHint,
            codeExample,
            language: this.getLanguageInfo(language)
        };
    }

    getBaseHint(problem, level) {
        // This would integrate with the existing hint system
        return `Base hint for ${problem.title} at ${level} level`;
    }

    generateCodeExample(problem, language, level) {
        return this.codeTemplateGenerator.generate(problem, language, level);
    }

    initializeSupportedLanguages() {
        return {
            'javascript': {
                name: 'JavaScript',
                extension: '.js',
                syntax: 'javascript',
                features: ['dynamic-typing', 'functional', 'object-oriented'],
                commonDataStructures: ['Array', 'Object', 'Map', 'Set'],
                tips: [
                    'Use Map() for hash maps instead of plain objects',
                    'Array methods like filter(), map(), reduce() are very useful',
                    'Consider using Set for unique values'
                ]
            },
            'python': {
                name: 'Python',
                extension: '.py',
                syntax: 'python',
                features: ['dynamic-typing', 'object-oriented', 'functional'],
                commonDataStructures: ['list', 'dict', 'set', 'tuple'],
                tips: [
                    'Use dict() for hash maps',
                    'List comprehensions can make code more concise',
                    'collections module has useful data structures'
                ]
            },
            'java': {
                name: 'Java',
                extension: '.java',
                syntax: 'java',
                features: ['static-typing', 'object-oriented'],
                commonDataStructures: ['ArrayList', 'HashMap', 'HashSet', 'LinkedList'],
                tips: [
                    'Use HashMap<> for hash maps',
                    'ArrayList<> for dynamic arrays',
                    'Remember to handle null values'
                ]
            },
            'cpp': {
                name: 'C++',
                extension: '.cpp',
                syntax: 'cpp',
                features: ['static-typing', 'object-oriented', 'low-level'],
                commonDataStructures: ['vector', 'unordered_map', 'unordered_set', 'queue'],
                tips: [
                    'Use unordered_map for hash maps',
                    'vector for dynamic arrays',
                    'Be careful with memory management'
                ]
            },
            'csharp': {
                name: 'C#',
                extension: '.cs',
                syntax: 'csharp',
                features: ['static-typing', 'object-oriented'],
                commonDataStructures: ['List', 'Dictionary', 'HashSet', 'Queue'],
                tips: [
                    'Use Dictionary<> for hash maps',
                    'List<> for dynamic arrays',
                    'LINQ methods can simplify operations'
                ]
            },
            'typescript': {
                name: 'TypeScript',
                extension: '.ts',
                syntax: 'typescript',
                features: ['static-typing', 'object-oriented', 'functional'],
                commonDataStructures: ['Array', 'Map', 'Set', 'Object'],
                tips: [
                    'Type annotations help catch errors',
                    'Use Map<> for hash maps with type safety',
                    'Interfaces can define data structures'
                ]
            },
            'go': {
                name: 'Go',
                extension: '.go',
                syntax: 'go',
                features: ['static-typing', 'concurrent'],
                commonDataStructures: ['slice', 'map', 'channel'],
                tips: [
                    'Use map[type]type for hash maps',
                    'Slices are dynamic arrays',
                    'Error handling is explicit'
                ]
            },
            'rust': {
                name: 'Rust',
                extension: '.rs',
                syntax: 'rust',
                features: ['static-typing', 'memory-safe', 'concurrent'],
                commonDataStructures: ['Vec', 'HashMap', 'HashSet', 'BTreeMap'],
                tips: [
                    'Use HashMap for hash maps',
                    'Vec<> for dynamic arrays',
                    'Ownership system prevents memory errors'
                ]
            },
            'swift': {
                name: 'Swift',
                extension: '.swift',
                syntax: 'swift',
                features: ['static-typing', 'object-oriented', 'functional'],
                commonDataStructures: ['Array', 'Dictionary', 'Set'],
                tips: [
                    'Use Dictionary for hash maps',
                    'Array for dynamic arrays',
                    'Optionals handle null values safely'
                ]
            },
            'kotlin': {
                name: 'Kotlin',
                extension: '.kt',
                syntax: 'kotlin',
                features: ['static-typing', 'object-oriented', 'functional'],
                commonDataStructures: ['List', 'Map', 'Set', 'MutableList'],
                tips: [
                    'Use mutableMapOf() for hash maps',
                    'mutableListOf() for dynamic arrays',
                    'Null safety is built-in'
                ]
            }
        };
    }
}

// Syntax Highlighting System
class SyntaxHighlighter {
    highlight(code, language) {
        // Basic syntax highlighting - in a real implementation, 
        // you might use a library like Prism.js or highlight.js
        const highlightRules = this.getHighlightRules(language);
        let highlightedCode = code;

        for (const [pattern, className] of highlightRules) {
            highlightedCode = highlightedCode.replace(pattern, `<span class="${className}">$&</span>`);
        }

        return highlightedCode;
    }

    getHighlightRules(language) {
        const rules = {
            'javascript': [
                [/\b(function|const|let|var|if|else|for|while|return)\b/g, 'keyword'],
                [/\b(true|false|null|undefined)\b/g, 'literal'],
                [/"[^"]*"/g, 'string'],
                [/'[^']*'/g, 'string'],
                [/\/\/.*$/gm, 'comment']
            ],
            'python': [
                [/\b(def|class|if|else|elif|for|while|return|import|from)\b/g, 'keyword'],
                [/\b(True|False|None)\b/g, 'literal'],
                [/"[^"]*"/g, 'string'],
                [/'[^']*'/g, 'string'],
                [/#.*$/gm, 'comment']
            ],
            'java': [
                [/\b(public|private|static|class|if|else|for|while|return|new)\b/g, 'keyword'],
                [/\b(true|false|null)\b/g, 'literal'],
                [/"[^"]*"/g, 'string'],
                [/\/\/.*$/gm, 'comment']
            ]
        };

        return rules[language] || rules['javascript'];
    }
}

// Code Template Generation System
class CodeTemplateGenerator {
    generate(problem, language, level) {
        const templates = this.getTemplates(language);
        const problemType = this.identifyProblemType(problem);
        
        if (level === 'strong' && templates[problemType]) {
            return templates[problemType];
        }

        return templates.basic || this.getBasicTemplate(language);
    }

    identifyProblemType(problem) {
        const title = problem.title.toLowerCase();
        
        if (title.includes('two sum')) return 'twoSum';
        if (title.includes('palindrome')) return 'palindrome';
        if (title.includes('tree')) return 'tree';
        if (title.includes('linked list')) return 'linkedList';
        
        return 'basic';
    }

    getTemplates(language) {
        const templates = {
            'javascript': {
                basic: `// JavaScript solution template
function solve(input) {
    // Your solution here
    return result;
}`,
                twoSum: `// Two Sum - JavaScript
function twoSum(nums, target) {
    const map = new Map();
    
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
    
    return [];
}`
            },
            'python': {
                basic: `# Python solution template
def solve(input):
    # Your solution here
    return result`,
                twoSum: `# Two Sum - Python
def twoSum(nums, target):
    num_map = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_map:
            return [num_map[complement], i]
        num_map[num] = i
    
    return []`
            },
            'java': {
                basic: `// Java solution template
public class Solution {
    public ReturnType solve(InputType input) {
        // Your solution here
        return result;
    }
}`,
                twoSum: `// Two Sum - Java
public class Solution {
    public int[] twoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();
        
        for (int i = 0; i < nums.length; i++) {
            int complement = target - nums[i];
            if (map.containsKey(complement)) {
                return new int[]{map.get(complement), i};
            }
            map.put(nums[i], i);
        }
        
        return new int[]{};
    }
}`
            }
        };

        return templates[language] || templates['javascript'];
    }

    getBasicTemplate(language) {
        const basicTemplates = {
            'javascript': 'function solve() {\n    // Your solution here\n}',
            'python': 'def solve():\n    # Your solution here\n    pass',
            'java': 'public class Solution {\n    public void solve() {\n        // Your solution here\n    }\n}',
            'cpp': '#include <iostream>\nusing namespace std;\n\nint main() {\n    // Your solution here\n    return 0;\n}'
        };

        return basicTemplates[language] || basicTemplates['javascript'];
    }
}

// Language-Specific Hints System
class LanguageSpecificHints {
    getHint(problem, level, language) {
        const hintDatabase = this.getLanguageHints();
        const problemKey = problem.slug || problem.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
        
        if (hintDatabase[language] && hintDatabase[language][problemKey]) {
            return hintDatabase[language][problemKey][level];
        }

        return this.getGenericLanguageHint(language, level);
    }

    getLanguageHints() {
        return {
            'javascript': {
                'two-sum': {
                    gentle: 'In JavaScript, use Map() for O(1) lookups instead of objects.',
                    moderate: 'Use map.has() to check existence and map.get() to retrieve values.',
                    strong: 'const map = new Map(); map.set(nums[i], i); if (map.has(complement)) return [map.get(complement), i];'
                }
            },
            'python': {
                'two-sum': {
                    gentle: 'Python dictionaries provide O(1) average lookup time.',
                    moderate: 'Use "in" operator to check if key exists in dictionary.',
                    strong: 'num_map = {}; if complement in num_map: return [num_map[complement], i]; num_map[num] = i'
                }
            },
            'java': {
                'two-sum': {
                    gentle: 'Use HashMap<Integer, Integer> for efficient key-value storage.',
                    moderate: 'Use containsKey() method to check if complement exists.',
                    strong: 'Map<Integer, Integer> map = new HashMap<>(); if (map.containsKey(complement)) return new int[]{map.get(complement), i};'
                }
            }
        };
    }

    getGenericLanguageHint(language, level) {
        const genericHints = {
            'javascript': {
                gentle: 'Consider using JavaScript\'s built-in data structures like Map and Set.',
                moderate: 'Use array methods like filter(), map(), and reduce() for cleaner code.',
                strong: 'Remember that JavaScript has dynamic typing - be careful with type coercion.'
            },
            'python': {
                gentle: 'Python\'s built-in data structures are very powerful and optimized.',
                moderate: 'List comprehensions and dictionary comprehensions can make code more readable.',
                strong: 'Use enumerate() for index-value pairs and zip() for parallel iteration.'
            },
            'java': {
                gentle: 'Java\'s Collections framework provides efficient data structures.',
                moderate: 'Use generics for type safety: ArrayList<Integer>, HashMap<String, Integer>.',
                strong: 'Remember to handle null values and consider using Optional for better null safety.'
            }
        };

        return genericHints[language]?.[level] || 'Consider the language-specific best practices for this problem.';
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        LanguageSupport, 
        SyntaxHighlighter, 
        CodeTemplateGenerator, 
        LanguageSpecificHints 
    };
}
