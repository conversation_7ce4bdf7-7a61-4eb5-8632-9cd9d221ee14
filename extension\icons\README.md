# LeetCode Helper Pro Icons

This directory contains the icon files for the LeetCode Helper Pro extension.

## Icon Sizes Required

- **icon16.png** - 16x16 pixels - Used in the extension toolbar
- **icon32.png** - 32x32 pixels - Used in the extension management page
- **icon48.png** - 48x48 pixels - Used in the extension management page and notifications
- **icon128.png** - 128x128 pixels - Used in the Chrome Web Store and extension management page

## Icon Design

The icons should feature:
- A brain or lightbulb symbol representing intelligence and hints
- Modern gradient colors matching the extension theme (#667eea to #764ba2)
- Clean, professional design that works at all sizes
- High contrast for visibility

## Creating Icons

You can create these icons using:
1. **Online tools**: Canva, Figma, or similar design tools
2. **AI generators**: DALL-E, Midjourney, or Stable Diffusion
3. **Icon libraries**: Feather Icons, Heroicons, or Font Awesome
4. **Design software**: Adobe Illustrator, Sketch, or GIMP

## Placeholder Icons

For development and testing, you can use simple colored squares or text-based icons until proper icons are created.

## Installation Note

Make sure all four icon files are present in this directory before loading the extension, or Chrome will show warnings about missing icons.
