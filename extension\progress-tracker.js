// Progress Tracking System for LeetCode Helper Pro
class ProgressTracker {
    constructor() {
        this.userStats = {
            problemsSolved: 0,
            hintsUsed: 0,
            totalTimeSpent: 0,
            streakDays: 0,
            lastActiveDate: null,
            difficultyBreakdown: {
                easy: 0,
                medium: 0,
                hard: 0
            },
            categoryBreakdown: {},
            languageUsage: {},
            badges: [],
            achievements: [],
            weeklyGoals: {
                problemsTarget: 5,
                problemsCompleted: 0,
                weekStart: null
            }
        };
        
        this.badgeSystem = new BadgeSystem();
        this.achievementSystem = new AchievementSystem();
        this.analyticsEngine = new AnalyticsEngine();
        
        this.init();
    }

    async init() {
        await this.loadUserStats();
        this.updateStreak();
        this.checkWeeklyReset();
    }

    async loadUserStats() {
        try {
            const result = await chrome.storage.sync.get(['userStats']);
            if (result.userStats) {
                this.userStats = { ...this.userStats, ...result.userStats };
            }
        } catch (error) {
            console.error('Error loading user stats:', error);
        }
    }

    async saveUserStats() {
        try {
            await chrome.storage.sync.set({ userStats: this.userStats });
        } catch (error) {
            console.error('Error saving user stats:', error);
        }
    }

    trackProblemSolved(problem, timeSpent, hintsUsed = 0) {
        this.userStats.problemsSolved++;
        this.userStats.hintsUsed += hintsUsed;
        this.userStats.totalTimeSpent += timeSpent;
        this.userStats.lastActiveDate = new Date().toISOString();

        // Update difficulty breakdown
        const difficulty = problem.difficulty.toLowerCase();
        if (this.userStats.difficultyBreakdown[difficulty] !== undefined) {
            this.userStats.difficultyBreakdown[difficulty]++;
        }

        // Update category breakdown
        const category = problem.category || 'General';
        this.userStats.categoryBreakdown[category] = (this.userStats.categoryBreakdown[category] || 0) + 1;

        // Update weekly goals
        this.userStats.weeklyGoals.problemsCompleted++;

        // Check for new badges and achievements
        this.checkForNewBadges();
        this.checkForNewAchievements();

        this.saveUserStats();
        this.notifyProgressUpdate();
    }

    trackLanguageUsage(language) {
        this.userStats.languageUsage[language] = (this.userStats.languageUsage[language] || 0) + 1;
        this.saveUserStats();
    }

    trackHintUsage(level, problem) {
        this.userStats.hintsUsed++;
        
        // Track hint effectiveness (could be expanded)
        const hintData = {
            level,
            problem: problem.slug,
            timestamp: Date.now()
        };

        this.saveUserStats();
    }

    updateStreak() {
        const today = new Date();
        const lastActive = this.userStats.lastActiveDate ? new Date(this.userStats.lastActiveDate) : null;

        if (!lastActive) {
            this.userStats.streakDays = 0;
            return;
        }

        const daysDiff = Math.floor((today - lastActive) / (1000 * 60 * 60 * 24));

        if (daysDiff === 0) {
            // Same day, maintain streak
            return;
        } else if (daysDiff === 1) {
            // Consecutive day, increment streak
            this.userStats.streakDays++;
        } else {
            // Streak broken
            this.userStats.streakDays = 0;
        }

        this.saveUserStats();
    }

    checkWeeklyReset() {
        const now = new Date();
        const weekStart = this.userStats.weeklyGoals.weekStart ? new Date(this.userStats.weeklyGoals.weekStart) : null;

        if (!weekStart || this.isNewWeek(weekStart, now)) {
            this.userStats.weeklyGoals.problemsCompleted = 0;
            this.userStats.weeklyGoals.weekStart = this.getWeekStart(now).toISOString();
            this.saveUserStats();
        }
    }

    isNewWeek(weekStart, now) {
        const weekStartTime = this.getWeekStart(weekStart);
        const currentWeekStart = this.getWeekStart(now);
        return weekStartTime.getTime() !== currentWeekStart.getTime();
    }

    getWeekStart(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day;
        return new Date(d.setDate(diff));
    }

    checkForNewBadges() {
        const newBadges = this.badgeSystem.checkEligibleBadges(this.userStats);
        
        newBadges.forEach(badge => {
            if (!this.userStats.badges.find(b => b.id === badge.id)) {
                this.userStats.badges.push({
                    ...badge,
                    earnedDate: new Date().toISOString()
                });
                this.showBadgeNotification(badge);
            }
        });
    }

    checkForNewAchievements() {
        const newAchievements = this.achievementSystem.checkEligibleAchievements(this.userStats);
        
        newAchievements.forEach(achievement => {
            if (!this.userStats.achievements.find(a => a.id === achievement.id)) {
                this.userStats.achievements.push({
                    ...achievement,
                    earnedDate: new Date().toISOString()
                });
                this.showAchievementNotification(achievement);
            }
        });
    }

    showBadgeNotification(badge) {
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'New Badge Earned! 🏆',
                message: `You've earned the "${badge.name}" badge!`
            });
        }
    }

    showAchievementNotification(achievement) {
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Achievement Unlocked! 🎉',
                message: `${achievement.name}: ${achievement.description}`
            });
        }
    }

    notifyProgressUpdate() {
        // Send message to popup and content scripts about progress update
        chrome.runtime.sendMessage({
            type: 'PROGRESS_UPDATED',
            stats: this.userStats
        });
    }

    getProgressSummary() {
        return {
            ...this.userStats,
            analytics: this.analyticsEngine.generateAnalytics(this.userStats),
            recommendations: this.generateRecommendations()
        };
    }

    generateRecommendations() {
        const recommendations = [];

        // Difficulty recommendations
        const { easy, medium, hard } = this.userStats.difficultyBreakdown;
        const total = easy + medium + hard;

        if (total > 0) {
            const easyPercent = (easy / total) * 100;
            const mediumPercent = (medium / total) * 100;
            const hardPercent = (hard / total) * 100;

            if (easyPercent > 70) {
                recommendations.push({
                    type: 'difficulty',
                    message: 'Try more medium difficulty problems to challenge yourself!',
                    action: 'focus_medium'
                });
            } else if (hardPercent < 10 && total > 20) {
                recommendations.push({
                    type: 'difficulty',
                    message: 'Consider attempting some hard problems to expand your skills.',
                    action: 'try_hard'
                });
            }
        }

        // Streak recommendations
        if (this.userStats.streakDays === 0) {
            recommendations.push({
                type: 'streak',
                message: 'Start a solving streak! Try to solve at least one problem daily.',
                action: 'start_streak'
            });
        } else if (this.userStats.streakDays >= 7) {
            recommendations.push({
                type: 'streak',
                message: `Amazing ${this.userStats.streakDays}-day streak! Keep it up!`,
                action: 'maintain_streak'
            });
        }

        // Weekly goal recommendations
        const weeklyProgress = this.userStats.weeklyGoals.problemsCompleted / this.userStats.weeklyGoals.problemsTarget;
        if (weeklyProgress < 0.5) {
            recommendations.push({
                type: 'weekly',
                message: 'You\'re behind on your weekly goal. Try to solve a few more problems!',
                action: 'catch_up_weekly'
            });
        }

        return recommendations;
    }

    setWeeklyGoal(target) {
        this.userStats.weeklyGoals.problemsTarget = target;
        this.saveUserStats();
    }

    getDetailedStats() {
        return {
            overview: {
                problemsSolved: this.userStats.problemsSolved,
                hintsUsed: this.userStats.hintsUsed,
                averageHintsPerProblem: this.userStats.problemsSolved > 0 ? 
                    (this.userStats.hintsUsed / this.userStats.problemsSolved).toFixed(1) : 0,
                totalTimeSpent: this.formatTime(this.userStats.totalTimeSpent),
                currentStreak: this.userStats.streakDays
            },
            difficulty: this.userStats.difficultyBreakdown,
            categories: this.userStats.categoryBreakdown,
            languages: this.userStats.languageUsage,
            badges: this.userStats.badges,
            achievements: this.userStats.achievements,
            weeklyProgress: {
                completed: this.userStats.weeklyGoals.problemsCompleted,
                target: this.userStats.weeklyGoals.problemsTarget,
                percentage: Math.round((this.userStats.weeklyGoals.problemsCompleted / this.userStats.weeklyGoals.problemsTarget) * 100)
            }
        };
    }

    formatTime(milliseconds) {
        const hours = Math.floor(milliseconds / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        }
        return `${minutes}m`;
    }

    exportProgress() {
        return {
            exportDate: new Date().toISOString(),
            stats: this.userStats,
            summary: this.getProgressSummary()
        };
    }

    importProgress(data) {
        if (data.stats) {
            this.userStats = { ...this.userStats, ...data.stats };
            this.saveUserStats();
            return true;
        }
        return false;
    }
}

// Badge System
class BadgeSystem {
    constructor() {
        this.badges = this.initializeBadges();
    }

    checkEligibleBadges(userStats) {
        const eligibleBadges = [];

        for (const badge of this.badges) {
            if (badge.checkCondition(userStats)) {
                eligibleBadges.push(badge);
            }
        }

        return eligibleBadges;
    }

    initializeBadges() {
        return [
            {
                id: 'first_solve',
                name: 'First Steps',
                description: 'Solved your first problem',
                icon: '🎯',
                rarity: 'common',
                checkCondition: (stats) => stats.problemsSolved >= 1
            },
            {
                id: 'problem_solver',
                name: 'Problem Solver',
                description: 'Solved 10 problems',
                icon: '🧩',
                rarity: 'common',
                checkCondition: (stats) => stats.problemsSolved >= 10
            },
            {
                id: 'dedicated_solver',
                name: 'Dedicated Solver',
                description: 'Solved 50 problems',
                icon: '💪',
                rarity: 'uncommon',
                checkCondition: (stats) => stats.problemsSolved >= 50
            },
            {
                id: 'coding_master',
                name: 'Coding Master',
                description: 'Solved 100 problems',
                icon: '👑',
                rarity: 'rare',
                checkCondition: (stats) => stats.problemsSolved >= 100
            },
            {
                id: 'streak_starter',
                name: 'Streak Starter',
                description: 'Maintained a 3-day solving streak',
                icon: '🔥',
                rarity: 'common',
                checkCondition: (stats) => stats.streakDays >= 3
            },
            {
                id: 'streak_master',
                name: 'Streak Master',
                description: 'Maintained a 30-day solving streak',
                icon: '⚡',
                rarity: 'legendary',
                checkCondition: (stats) => stats.streakDays >= 30
            },
            {
                id: 'hint_efficient',
                name: 'Hint Efficient',
                description: 'Solved 20 problems with minimal hints',
                icon: '🎪',
                rarity: 'uncommon',
                checkCondition: (stats) => stats.problemsSolved >= 20 && (stats.hintsUsed / stats.problemsSolved) < 1
            },
            {
                id: 'hard_challenger',
                name: 'Hard Challenger',
                description: 'Solved 5 hard problems',
                icon: '🏔️',
                rarity: 'rare',
                checkCondition: (stats) => stats.difficultyBreakdown.hard >= 5
            },
            {
                id: 'polyglot',
                name: 'Polyglot',
                description: 'Used 3 different programming languages',
                icon: '🌍',
                rarity: 'uncommon',
                checkCondition: (stats) => Object.keys(stats.languageUsage).length >= 3
            }
        ];
    }
}

// Achievement System
class AchievementSystem {
    constructor() {
        this.achievements = this.initializeAchievements();
    }

    checkEligibleAchievements(userStats) {
        const eligibleAchievements = [];

        for (const achievement of this.achievements) {
            if (achievement.checkCondition(userStats)) {
                eligibleAchievements.push(achievement);
            }
        }

        return eligibleAchievements;
    }

    initializeAchievements() {
        return [
            {
                id: 'speed_demon',
                name: 'Speed Demon',
                description: 'Solved a problem in under 5 minutes',
                points: 50,
                checkCondition: (stats) => {
                    // This would need to track individual problem solve times
                    return false; // Placeholder
                }
            },
            {
                id: 'category_master',
                name: 'Category Master',
                description: 'Solved 10 problems in the same category',
                points: 100,
                checkCondition: (stats) => {
                    return Object.values(stats.categoryBreakdown).some(count => count >= 10);
                }
            },
            {
                id: 'weekly_warrior',
                name: 'Weekly Warrior',
                description: 'Completed weekly goal 4 weeks in a row',
                points: 200,
                checkCondition: (stats) => {
                    // This would need to track weekly completion history
                    return false; // Placeholder
                }
            }
        ];
    }
}

// Analytics Engine
class AnalyticsEngine {
    generateAnalytics(userStats) {
        return {
            solvingTrends: this.analyzeSolvingTrends(userStats),
            difficultyProgression: this.analyzeDifficultyProgression(userStats),
            categoryPreferences: this.analyzeCategoryPreferences(userStats),
            languageUsage: this.analyzeLanguageUsage(userStats),
            hintUsagePatterns: this.analyzeHintUsage(userStats)
        };
    }

    analyzeSolvingTrends(userStats) {
        // Placeholder for trend analysis
        return {
            averageProblemsPerWeek: 3.5,
            peakSolvingDay: 'Sunday',
            improvementRate: 'Steady'
        };
    }

    analyzeDifficultyProgression(userStats) {
        const { easy, medium, hard } = userStats.difficultyBreakdown;
        const total = easy + medium + hard;

        return {
            easyPercentage: total > 0 ? Math.round((easy / total) * 100) : 0,
            mediumPercentage: total > 0 ? Math.round((medium / total) * 100) : 0,
            hardPercentage: total > 0 ? Math.round((hard / total) * 100) : 0,
            recommendation: this.getDifficultyRecommendation(easy, medium, hard)
        };
    }

    getDifficultyRecommendation(easy, medium, hard) {
        const total = easy + medium + hard;
        if (total === 0) return 'Start with easy problems';
        
        const easyPercent = (easy / total) * 100;
        if (easyPercent > 70) return 'Try more medium problems';
        if (hard === 0 && total > 10) return 'Challenge yourself with hard problems';
        
        return 'Good difficulty balance';
    }

    analyzeCategoryPreferences(userStats) {
        const categories = Object.entries(userStats.categoryBreakdown)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        return {
            topCategories: categories,
            suggestion: categories.length > 0 ? 
                `You excel at ${categories[0][0]} problems` : 
                'Explore different problem categories'
        };
    }

    analyzeLanguageUsage(userStats) {
        const languages = Object.entries(userStats.languageUsage)
            .sort(([,a], [,b]) => b - a);

        return {
            primaryLanguage: languages[0]?.[0] || 'None',
            languageCount: languages.length,
            suggestion: languages.length < 2 ? 
                'Try solving problems in different languages' : 
                'Great language diversity!'
        };
    }

    analyzeHintUsage(userStats) {
        const averageHints = userStats.problemsSolved > 0 ? 
            userStats.hintsUsed / userStats.problemsSolved : 0;

        return {
            averageHintsPerProblem: averageHints.toFixed(1),
            efficiency: averageHints < 1 ? 'Excellent' : 
                        averageHints < 2 ? 'Good' : 'Needs improvement',
            suggestion: averageHints > 2 ? 
                'Try to solve problems with fewer hints' : 
                'Great problem-solving independence!'
        };
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        ProgressTracker, 
        BadgeSystem, 
        AchievementSystem, 
        AnalyticsEngine 
    };
}
