"""
Database configuration and models for LeetCode Helper Pro AI Backend
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
from datetime import datetime
import asyncio
from app.core.config import settings

# Create database engine
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()


class ProblemAnalysis(Base):
    """Store AI analysis results for problems"""
    __tablename__ = "problem_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    problem_slug = Column(String(255), unique=True, index=True)
    problem_title = Column(String(500))
    difficulty = Column(String(50))
    category = Column(String(100))
    
    # AI Analysis results
    ai_analysis = Column(Text)  # JSON string of analysis
    approach_suggestions = Column(Text)  # JSON string of approaches
    complexity_analysis = Column(Text)  # JSON string of complexity info
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    analysis_version = Column(String(50), default="1.0")


class HintGeneration(Base):
    """Store generated hints for problems"""
    __tablename__ = "hint_generations"
    
    id = Column(Integer, primary_key=True, index=True)
    problem_slug = Column(String(255), index=True)
    hint_level = Column(String(50))  # gentle, moderate, strong
    hint_content = Column(Text)
    
    # Context information
    user_context = Column(Text)  # JSON string of user context
    language = Column(String(50))
    
    # Quality metrics
    effectiveness_score = Column(Float, default=0.0)
    user_feedback = Column(Integer, default=0)  # -1, 0, 1 for bad, neutral, good
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    ai_model_used = Column(String(100))


class YouTubeRecommendation(Base):
    """Store YouTube video recommendations"""
    __tablename__ = "youtube_recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    problem_slug = Column(String(255), index=True)
    video_id = Column(String(100))
    video_title = Column(String(500))
    channel_name = Column(String(200))
    video_url = Column(String(500))
    
    # Quality metrics
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    duration = Column(String(50))
    relevance_score = Column(Float, default=0.0)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_verified = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)


class UserInteraction(Base):
    """Track user interactions for analytics"""
    __tablename__ = "user_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    problem_slug = Column(String(255), index=True)
    
    # Interaction details
    interaction_type = Column(String(100))  # hint_request, analysis_request, youtube_request
    interaction_data = Column(Text)  # JSON string of interaction details
    
    # User struggle detection
    hint_requests_count = Column(Integer, default=0)
    time_spent_seconds = Column(Integer, default=0)
    struggle_indicators = Column(Text)  # JSON string of struggle signals
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    user_agent = Column(String(500))
    ip_address = Column(String(50))


class AIModelPerformance(Base):
    """Track AI model performance metrics"""
    __tablename__ = "ai_model_performance"
    
    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String(100), index=True)
    endpoint = Column(String(200))
    
    # Performance metrics
    response_time_ms = Column(Integer)
    token_count = Column(Integer)
    success = Column(Boolean)
    error_message = Column(Text)
    
    # Quality metrics
    user_satisfaction = Column(Float)  # 0.0 to 1.0
    hint_effectiveness = Column(Float)  # 0.0 to 1.0
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    request_id = Column(String(255))


# Database utility functions
async def init_db():
    """Initialize database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully")
    except Exception as e:
        print(f"Error creating database tables: {e}")
        raise


def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Database operations
class DatabaseManager:
    """Database operations manager"""
    
    @staticmethod
    def save_problem_analysis(db, problem_slug: str, analysis_data: dict):
        """Save problem analysis to database"""
        try:
            analysis = ProblemAnalysis(
                problem_slug=problem_slug,
                problem_title=analysis_data.get("title"),
                difficulty=analysis_data.get("difficulty"),
                category=analysis_data.get("category"),
                ai_analysis=str(analysis_data.get("analysis", {})),
                approach_suggestions=str(analysis_data.get("approaches", {})),
                complexity_analysis=str(analysis_data.get("complexity", {}))
            )
            
            # Check if analysis already exists
            existing = db.query(ProblemAnalysis).filter(
                ProblemAnalysis.problem_slug == problem_slug
            ).first()
            
            if existing:
                # Update existing analysis
                existing.ai_analysis = analysis.ai_analysis
                existing.approach_suggestions = analysis.approach_suggestions
                existing.complexity_analysis = analysis.complexity_analysis
                existing.updated_at = datetime.utcnow()
            else:
                # Create new analysis
                db.add(analysis)
            
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error saving problem analysis: {e}")
            return False
    
    @staticmethod
    def save_hint_generation(db, problem_slug: str, hint_data: dict):
        """Save generated hint to database"""
        try:
            hint = HintGeneration(
                problem_slug=problem_slug,
                hint_level=hint_data.get("level"),
                hint_content=hint_data.get("content"),
                user_context=str(hint_data.get("context", {})),
                language=hint_data.get("language"),
                ai_model_used=hint_data.get("model")
            )
            
            db.add(hint)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error saving hint generation: {e}")
            return False
    
    @staticmethod
    def save_youtube_recommendation(db, problem_slug: str, video_data: dict):
        """Save YouTube recommendation to database"""
        try:
            recommendation = YouTubeRecommendation(
                problem_slug=problem_slug,
                video_id=video_data.get("video_id"),
                video_title=video_data.get("title"),
                channel_name=video_data.get("channel"),
                video_url=video_data.get("url"),
                view_count=video_data.get("view_count", 0),
                duration=video_data.get("duration"),
                relevance_score=video_data.get("relevance_score", 0.0)
            )
            
            db.add(recommendation)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error saving YouTube recommendation: {e}")
            return False
    
    @staticmethod
    def track_user_interaction(db, interaction_data: dict):
        """Track user interaction for analytics"""
        try:
            interaction = UserInteraction(
                session_id=interaction_data.get("session_id"),
                problem_slug=interaction_data.get("problem_slug"),
                interaction_type=interaction_data.get("type"),
                interaction_data=str(interaction_data.get("data", {})),
                hint_requests_count=interaction_data.get("hint_count", 0),
                time_spent_seconds=interaction_data.get("time_spent", 0),
                user_agent=interaction_data.get("user_agent"),
                ip_address=interaction_data.get("ip_address")
            )
            
            db.add(interaction)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error tracking user interaction: {e}")
            return False
