"""
LeetCode Helper Pro - AI Backend
Main FastAPI application entry point
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
import logging

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.routes import api_router
from app.services.ai_service import AIService
from app.services.youtube_service import YouTubeService
from app.core.database import init_db

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global services
ai_service = None
youtube_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global ai_service, youtube_service
    
    logger.info("Starting LeetCode Helper Pro AI Backend...")
    
    # Initialize database
    await init_db()
    
    # Initialize AI services
    ai_service = AIService()
    await ai_service.initialize()
    
    # Initialize YouTube service
    youtube_service = YouTubeService()
    await youtube_service.initialize()
    
    logger.info("All services initialized successfully")
    
    yield
    
    # Cleanup
    logger.info("Shutting down services...")
    if ai_service:
        await ai_service.cleanup()
    if youtube_service:
        await youtube_service.cleanup()


# Create FastAPI app
app = FastAPI(
    title="LeetCode Helper Pro AI Backend",
    description="AI-powered backend for LeetCode problem analysis and hint generation",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "services": {
            "ai_service": ai_service is not None and ai_service.is_ready(),
            "youtube_service": youtube_service is not None and youtube_service.is_ready()
        }
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Include API routes
app.include_router(api_router, prefix="/api/v1")


# Dependency to get AI service
async def get_ai_service() -> AIService:
    if ai_service is None:
        raise HTTPException(status_code=503, detail="AI service not available")
    return ai_service


# Dependency to get YouTube service
async def get_youtube_service() -> YouTubeService:
    if youtube_service is None:
        raise HTTPException(status_code=503, detail="YouTube service not available")
    return youtube_service


# Make services available globally
app.state.ai_service = ai_service
app.state.youtube_service = youtube_service


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )
