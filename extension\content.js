// Content Script for LeetCode Helper Pro
class LeetCodeProblemDetector {
    constructor() {
        this.currentProblem = null;
        this.observer = null;
        this.init();
    }

    init() {
        this.detectProblem();
        this.setupMessageListener();
        this.setupMutationObserver();
        this.injectHelperUI();
    }

    detectProblem() {
        try {
            const problem = this.extractProblemData();
            if (problem && problem.title) {
                this.currentProblem = problem;
                this.notifyExtension(problem);
                this.updateHelperUI(problem);
            }
        } catch (error) {
            console.error('Error detecting problem:', error);
        }
    }

    extractProblemData() {
        const url = window.location.href;
        
        // Check if we're on a problem page
        if (!url.includes('/problems/')) {
            return null;
        }

        // Extract problem slug from URL
        const problemSlug = url.split('/problems/')[1]?.split('/')[0];
        if (!problemSlug) return null;

        // Try multiple selectors for different LeetCode layouts
        const titleSelectors = [
            '[data-cy="question-title"]',
            '.css-v3d350',
            '.question-title h3',
            '.question-title',
            'h4[class*="title"]',
            '[class*="question-title"]'
        ];

        const difficultySelectors = [
            '[diff]',
            '.difficulty',
            '[class*="difficulty"]',
            '.css-10o4wqw',
            '.question-difficulty'
        ];

        let title = null;
        let difficulty = null;
        let description = null;
        let acceptance = null;

        // Extract title
        for (const selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                title = element.textContent.trim();
                break;
            }
        }

        // Extract difficulty
        for (const selector of difficultySelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                difficulty = element.textContent.trim();
                break;
            }
        }

        // Try to extract from page data or meta tags
        if (!title || !difficulty) {
            const scriptTags = document.querySelectorAll('script');
            for (const script of scriptTags) {
                if (script.textContent.includes('questionData') || script.textContent.includes('question')) {
                    try {
                        const content = script.textContent;
                        const titleMatch = content.match(/"title":\s*"([^"]+)"/);
                        const difficultyMatch = content.match(/"difficulty":\s*"([^"]+)"/);
                        
                        if (titleMatch && !title) title = titleMatch[1];
                        if (difficultyMatch && !difficulty) difficulty = difficultyMatch[1];
                    } catch (e) {
                        // Continue searching
                    }
                }
            }
        }

        // Extract description
        const descriptionElement = document.querySelector('.question-content, [class*="question-content"], .content__u3I1');
        if (descriptionElement) {
            description = descriptionElement.textContent.trim().substring(0, 500) + '...';
        }

        // Extract acceptance rate
        const acceptanceElement = document.querySelector('[class*="acceptance"], .acceptance-rate');
        if (acceptanceElement) {
            acceptance = acceptanceElement.textContent.trim();
        }

        // Fallback: use URL slug as title if nothing found
        if (!title && problemSlug) {
            title = problemSlug.split('-').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ');
        }

        // Determine category based on common patterns
        const category = this.categorizeProlem(title, description);

        return {
            title: title || 'Unknown Problem',
            difficulty: difficulty || 'Unknown',
            description: description || 'No description available',
            acceptance: acceptance || 'N/A',
            category: category,
            slug: problemSlug,
            url: url
        };
    }

    categorizeProlem(title, description) {
        const categories = {
            'Array': ['array', 'subarray', 'matrix', 'grid'],
            'String': ['string', 'substring', 'palindrome', 'anagram'],
            'Linked List': ['linked list', 'node', 'next'],
            'Tree': ['tree', 'binary tree', 'root', 'leaf'],
            'Graph': ['graph', 'node', 'edge', 'path'],
            'Dynamic Programming': ['dp', 'dynamic', 'optimal', 'subproblem'],
            'Hash Table': ['hash', 'map', 'dictionary'],
            'Two Pointers': ['two pointer', 'left', 'right'],
            'Binary Search': ['binary search', 'sorted', 'search'],
            'Backtracking': ['backtrack', 'permutation', 'combination'],
            'Math': ['math', 'number', 'digit', 'calculation']
        };

        const text = (title + ' ' + (description || '')).toLowerCase();
        
        for (const [category, keywords] of Object.entries(categories)) {
            if (keywords.some(keyword => text.includes(keyword))) {
                return category;
            }
        }

        return 'General';
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.type) {
                case 'GET_PROBLEM_DATA':
                    sendResponse({ problem: this.currentProblem });
                    break;

                case 'SHOW_QUICK_HINT':
                    this.showQuickHints();
                    break;

                case 'SHOW_SPECIFIC_HINT':
                    this.showSpecificHint(message.level);
                    break;

                case 'SHOW_APPROACH_ANALYSIS':
                    this.showApproachAnalysisModal();
                    break;

                case 'SHOW_TEST_CASES':
                    this.showTestCasesModal();
                    break;
            }
        });
    }

    setupMutationObserver() {
        // Watch for DOM changes to detect navigation
        this.observer = new MutationObserver((mutations) => {
            let shouldRedetect = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // Check if URL changed or important elements were added
                    if (mutation.addedNodes.length > 0) {
                        shouldRedetect = true;
                    }
                }
            });

            if (shouldRedetect) {
                setTimeout(() => this.detectProblem(), 1000);
            }
        });

        this.observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    notifyExtension(problem) {
        chrome.runtime.sendMessage({
            type: 'PROBLEM_DETECTED',
            problem: problem
        });
    }

    injectHelperUI() {
        // Create floating helper button
        const helperButton = document.createElement('div');
        helperButton.id = 'leetcode-helper-float';
        helperButton.innerHTML = `
            <div class="helper-btn">
                <span class="helper-icon">🧠</span>
                <span class="helper-text">Helper</span>
            </div>
        `;
        
        helperButton.addEventListener('click', () => {
            this.showQuickHints();
        });

        document.body.appendChild(helperButton);
    }

    updateHelperUI(problem) {
        const helperButton = document.getElementById('leetcode-helper-float');
        if (helperButton) {
            helperButton.classList.add('active');
            helperButton.title = `LeetCode Helper - ${problem.title}`;
        }
    }

    showQuickHints() {
        if (!this.currentProblem) return;

        // Create quick hints overlay
        const overlay = document.createElement('div');
        overlay.id = 'leetcode-helper-overlay';
        overlay.innerHTML = `
            <div class="quick-hints-panel">
                <div class="panel-header">
                    <h3>Quick Hints - ${this.currentProblem.title}</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="panel-body">
                    <div class="hint-buttons">
                        <button class="quick-hint-btn gentle" data-level="gentle">
                            💡 Gentle Hint
                        </button>
                        <button class="quick-hint-btn moderate" data-level="moderate">
                            🔍 Moderate Hint
                        </button>
                        <button class="quick-hint-btn strong" data-level="strong">
                            🎯 Strong Hint
                        </button>
                    </div>
                    <div class="hint-display" id="quickHintDisplay">
                        Select a hint level above
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        overlay.querySelector('.close-btn').addEventListener('click', () => {
            overlay.remove();
        });

        overlay.querySelectorAll('.quick-hint-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const level = e.target.dataset.level;
                this.displayQuickHint(level);
            });
        });

        document.body.appendChild(overlay);
    }

    displayQuickHint(level) {
        const display = document.getElementById('quickHintDisplay');
        const hints = this.generateMockHint(level);
        
        display.innerHTML = `
            <div class="hint-content ${level}">
                <strong>${level.charAt(0).toUpperCase() + level.slice(1)} Hint:</strong><br>
                ${hints}
            </div>
        `;
    }

    showSpecificHint(level) {
        if (!this.currentProblem) return;

        const hint = this.generateMockHint(level);
        this.showInlineHint(hint, level);
    }

    showInlineHint(hint, level) {
        // Remove existing inline hints
        const existingHints = document.querySelectorAll('.leetcode-helper-inline-hint');
        existingHints.forEach(hint => hint.remove());

        // Create inline hint element
        const hintElement = document.createElement('div');
        hintElement.className = 'leetcode-helper-inline-hint';
        hintElement.innerHTML = `
            <div class="inline-hint-header">
                <span class="hint-level ${level}">${level.charAt(0).toUpperCase() + level.slice(1)} Hint</span>
                <button class="inline-hint-close">&times;</button>
            </div>
            <div class="inline-hint-content">
                ${hint}
            </div>
        `;

        // Add close functionality
        hintElement.querySelector('.inline-hint-close').addEventListener('click', () => {
            hintElement.remove();
        });

        // Insert hint near the problem description
        const problemContent = document.querySelector('.question-content, [class*="question-content"], .content__u3I1');
        if (problemContent) {
            problemContent.parentNode.insertBefore(hintElement, problemContent.nextSibling);
        } else {
            document.body.appendChild(hintElement);
        }

        // Auto-remove after 30 seconds
        setTimeout(() => {
            if (hintElement.parentNode) {
                hintElement.remove();
            }
        }, 30000);
    }

    showApproachAnalysisModal() {
        if (!this.currentProblem) return;

        const modal = this.createModal('Approach Analysis', `
            <div class="approach-analysis">
                <div class="analysis-section">
                    <h4>Recommended Approach</h4>
                    <p>Hash Map / Two Pointer Technique</p>
                </div>
                <div class="analysis-section">
                    <h4>Time Complexity</h4>
                    <p>O(n) - Single pass through the data</p>
                </div>
                <div class="analysis-section">
                    <h4>Space Complexity</h4>
                    <p>O(n) - Additional storage required</p>
                </div>
                <div class="analysis-section">
                    <h4>Key Insights</h4>
                    <ul>
                        <li>Trade space for time efficiency</li>
                        <li>Avoid nested loops with smart data structures</li>
                        <li>Consider edge cases and constraints</li>
                    </ul>
                </div>
            </div>
        `);

        document.body.appendChild(modal);
    }

    showTestCasesModal() {
        if (!this.currentProblem) return;

        const modal = this.createModal('Generated Test Cases', `
            <div class="test-cases">
                <div class="test-case">
                    <h5>Basic Test Case</h5>
                    <div class="test-input">Input: [2,7,11,15], target = 9</div>
                    <div class="test-output">Output: [0,1]</div>
                    <div class="test-explanation">Explanation: nums[0] + nums[1] = 2 + 7 = 9</div>
                </div>
                <div class="test-case">
                    <h5>Edge Case</h5>
                    <div class="test-input">Input: [3,3], target = 6</div>
                    <div class="test-output">Output: [0,1]</div>
                    <div class="test-explanation">Explanation: Same value from different indices</div>
                </div>
                <div class="test-case">
                    <h5>Performance Test</h5>
                    <div class="test-input">Input: Large array with 10,000 elements</div>
                    <div class="test-output">Output: Indices of target sum</div>
                    <div class="test-explanation">Explanation: Tests algorithm efficiency</div>
                </div>
            </div>
        `);

        document.body.appendChild(modal);
    }

    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'leetcode-helper-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        // Add close functionality
        modal.querySelector('.modal-close').addEventListener('click', () => modal.remove());
        modal.querySelector('.modal-backdrop').addEventListener('click', () => modal.remove());

        return modal;
    }

    generateMockHint(level) {
        const problemTitle = this.currentProblem?.title || '';

        const hintDatabase = {
            gentle: {
                'Two Sum': 'Think about what data structure allows fast lookups...',
                'Add Two Numbers': 'Consider how you add numbers digit by digit...',
                'Longest Substring Without Repeating Characters': 'What if you kept track of characters you\'ve seen?',
                'default': 'Consider the problem constraints and think about efficient data structures.'
            },
            moderate: {
                'Two Sum': 'Use a hash map to store numbers you\'ve seen and their indices.',
                'Add Two Numbers': 'Process both linked lists simultaneously, handling carry values.',
                'Longest Substring Without Repeating Characters': 'Use a sliding window with a set to track unique characters.',
                'default': 'Break down the problem into smaller subproblems and consider optimal approaches.'
            },
            strong: {
                'Two Sum': 'Iterate through array, for each number check if (target - number) exists in hash map.',
                'Add Two Numbers': 'Create new linked list, add corresponding digits plus carry, handle different lengths.',
                'Longest Substring Without Repeating Characters': 'Use two pointers and a set. Expand right pointer, shrink left when duplicate found.',
                'default': 'Here\'s a detailed step-by-step approach to solve this problem efficiently.'
            }
        };

        return hintDatabase[level][problemTitle] || hintDatabase[level]['default'];
    }
}

// Initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new LeetCodeProblemDetector();
    });
} else {
    new LeetCodeProblemDetector();
}
