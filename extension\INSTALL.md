# Installation Guide - LeetCode Helper Pro

## Quick Start

### Chrome/Edge Installation
1. **Download the Extension**
   - Download or clone this repository to your computer
   - Extract the files if downloaded as a ZIP

2. **Open Extension Management**
   - Open Chrome or Edge browser
   - Navigate to `chrome://extensions/` (Chrome) or `edge://extensions/` (Edge)
   - Or click the three dots menu → More tools → Extensions

3. **Enable Developer Mode**
   - Toggle "Developer mode" switch in the top right corner

4. **Load the Extension**
   - Click "Load unpacked" button
   - Select the `extension` folder from this project
   - The extension should now appear in your extensions list

5. **Verify Installation**
   - Look for the LeetCode Helper Pro icon in your browser toolbar
   - Visit any LeetCode problem page to test functionality

### Firefox Installation
1. **Download the Extension**
   - Download or clone this repository to your computer

2. **Open Debug Page**
   - Open Firefox browser
   - Navigate to `about:debugging`
   - Click "This Firefox" in the left sidebar

3. **Load Temporary Add-on**
   - Click "Load Temporary Add-on..." button
   - Navigate to the `extension` folder
   - Select the `manifest.json` file

4. **Verify Installation**
   - The extension will be loaded temporarily
   - Visit any LeetCode problem page to test functionality

## Important Notes

### Icon Files
Before installation, you need to add icon files to the `extension/icons/` directory:
- `icon16.png` (16x16 pixels)
- `icon32.png` (32x32 pixels)  
- `icon48.png` (48x48 pixels)
- `icon128.png` (128x128 pixels)

You can create simple placeholder icons or use any 16x16, 32x32, 48x48, and 128x128 pixel images for testing.

### Permissions
The extension requests these permissions:
- **activeTab**: To interact with the current LeetCode tab
- **storage**: To save your progress and preferences
- **contextMenus**: To add right-click menu options
- **scripting**: To inject helper functionality into pages

### Supported Sites
- leetcode.com
- leetcode.cn (Chinese version)

## Testing the Extension

1. **Navigate to LeetCode**
   - Go to https://leetcode.com/problems/two-sum/ (or any problem)

2. **Test Basic Functionality**
   - Click the extension icon in the toolbar
   - The popup should show problem detection
   - Try clicking the hint buttons

3. **Test Context Menu**
   - Right-click on the problem page
   - Look for "LeetCode Helper Pro" in the context menu
   - Test the submenu options

4. **Test Floating Helper**
   - Look for a floating helper button on the right side of the page
   - Click it to open the quick hints panel

## Troubleshooting

### Extension Won't Load
- Make sure all files are in the `extension` folder
- Check that `manifest.json` is valid JSON
- Ensure you have the required icon files
- Try refreshing the extensions page

### No Problem Detection
- Refresh the LeetCode problem page
- Check browser console for errors (F12 → Console)
- Make sure you're on a supported LeetCode URL

### Features Not Working
- Check if the extension has proper permissions
- Try disabling and re-enabling the extension
- Clear browser cache and reload

### Console Errors
- Open browser developer tools (F12)
- Check Console tab for error messages
- Look for any failed network requests

## Development Mode

For developers who want to modify the extension:

1. **Make Changes**
   - Edit any files in the `extension` folder
   - Save your changes

2. **Reload Extension**
   - Go to the extensions management page
   - Click the refresh/reload button on the LeetCode Helper Pro extension
   - Or disable and re-enable the extension

3. **Test Changes**
   - Navigate to a LeetCode problem page
   - Test your modifications

## Uninstalling

### Chrome/Edge
1. Go to `chrome://extensions/` or `edge://extensions/`
2. Find LeetCode Helper Pro
3. Click "Remove" button
4. Confirm removal

### Firefox
1. Go to `about:addons`
2. Find LeetCode Helper Pro in the Extensions section
3. Click the three dots menu → Remove
4. Confirm removal

## Support

If you encounter issues:
1. Check this troubleshooting guide
2. Look at the browser console for error messages
3. Try the extension on different LeetCode problems
4. Ensure you're using a supported browser version

## Browser Compatibility

- **Chrome**: Version 88 or higher
- **Edge**: Version 88 or higher  
- **Firefox**: Version 78 or higher

## Next Steps

After successful installation:
1. Visit the main README.md for usage instructions
2. Explore all the features on different LeetCode problems
3. Customize settings through the popup interface
4. Track your progress as you solve problems

Happy coding! 🚀
