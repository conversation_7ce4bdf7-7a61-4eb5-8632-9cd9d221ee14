// AI Integration Service for LeetCode Helper Pro Extension
class AIIntegrationService {
    constructor() {
        this.baseURL = 'http://localhost:8000/api/v1';
        this.sessionId = this.generateSessionId();
        this.requestCache = new Map();
        this.isOnline = false;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async initialize() {
        try {
            await this.checkConnection();
            console.log('AI Integration Service initialized successfully');
        } catch (error) {
            console.warn('AI backend not available, using fallback mode:', error);
        }
    }

    async checkConnection() {
        try {
            const response = await fetch(`${this.baseURL}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.isOnline = data.status === 'healthy';
                return this.isOnline;
            }
        } catch (error) {
            this.isOnline = false;
            throw error;
        }
        return false;
    }

    async makeRequest(endpoint, data, method = 'POST') {
        // Check cache first for GET requests
        const cacheKey = `${method}_${endpoint}_${JSON.stringify(data)}`;
        if (method === 'GET' && this.requestCache.has(cacheKey)) {
            const cached = this.requestCache.get(cacheKey);
            if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
                return cached.data;
            }
        }

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(`${this.baseURL}${endpoint}`, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Session-ID': this.sessionId
                    },
                    body: method !== 'GET' ? JSON.stringify(data) : undefined
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                // Cache successful responses
                if (method === 'GET') {
                    this.requestCache.set(cacheKey, {
                        data: result,
                        timestamp: Date.now()
                    });
                }

                this.isOnline = true;
                return result;

            } catch (error) {
                console.warn(`AI request attempt ${attempt} failed:`, error);
                
                if (attempt === this.retryAttempts) {
                    this.isOnline = false;
                    throw error;
                }
                
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
            }
        }
    }

    async generateAIHint(problemData, level, userContext = {}) {
        if (!this.isOnline) {
            return this.getFallbackHint(problemData, level);
        }

        try {
            const requestData = {
                problem_data: {
                    title: problemData.title,
                    slug: problemData.slug,
                    difficulty: problemData.difficulty,
                    category: problemData.category,
                    description: problemData.description,
                    constraints: problemData.constraints || [],
                    tags: problemData.tags || [],
                    acceptance_rate: problemData.acceptance,
                    url: problemData.url
                },
                level: level,
                user_context: {
                    session_id: this.sessionId,
                    language: userContext.language || 'javascript',
                    previous_hints: userContext.previousHints || [],
                    time_spent: userContext.timeSpent || 0,
                    hint_count: userContext.hintCount || 0,
                    struggle_indicators: userContext.struggleIndicators || []
                }
            };

            const response = await this.makeRequest('/hint', requestData);
            
            return {
                success: true,
                hint: response.hint_text,
                level: response.level,
                followUpQuestions: response.follow_up_questions || [],
                keyConcepts: response.key_concepts || [],
                nextSteps: response.next_steps || [],
                encouragement: response.encouragement || '',
                source: 'ai',
                model: response.model
            };

        } catch (error) {
            console.error('AI hint generation failed:', error);
            return this.getFallbackHint(problemData, level);
        }
    }

    async analyzeProblem(problemData) {
        if (!this.isOnline) {
            return this.getFallbackAnalysis(problemData);
        }

        try {
            const requestData = {
                problem_data: {
                    title: problemData.title,
                    slug: problemData.slug,
                    difficulty: problemData.difficulty,
                    category: problemData.category,
                    description: problemData.description,
                    constraints: problemData.constraints || [],
                    tags: problemData.tags || [],
                    acceptance_rate: problemData.acceptance,
                    url: problemData.url
                },
                analysis_type: 'comprehensive',
                include_approaches: true,
                include_complexity: true
            };

            const response = await this.makeRequest('/analyze', requestData);
            
            return {
                success: true,
                problemType: response.problem_type,
                keyConcepts: response.key_concepts,
                optimalApproach: response.optimal_approach,
                alternativeApproaches: response.alternative_approaches || [],
                keyInsights: response.key_insights || [],
                commonPitfalls: response.common_pitfalls || [],
                edgeCases: response.edge_cases || [],
                difficultyJustification: response.difficulty_justification || '',
                source: 'ai'
            };

        } catch (error) {
            console.error('AI problem analysis failed:', error);
            return this.getFallbackAnalysis(problemData);
        }
    }

    async findYouTubeVideos(problemData, maxResults = 5) {
        if (!this.isOnline) {
            return this.getFallbackYouTubeResults(problemData);
        }

        try {
            const requestData = {
                problem_data: {
                    title: problemData.title,
                    slug: problemData.slug,
                    difficulty: problemData.difficulty,
                    category: problemData.category,
                    description: problemData.description,
                    constraints: problemData.constraints || [],
                    tags: problemData.tags || [],
                    acceptance_rate: problemData.acceptance,
                    url: problemData.url
                },
                max_results: maxResults,
                language_preference: 'english',
                difficulty_filter: null
            };

            const response = await this.makeRequest('/youtube', requestData);
            
            return {
                success: true,
                videos: response.videos.map(video => ({
                    id: video.video_id,
                    title: video.title,
                    channel: video.channel_name,
                    url: video.url,
                    thumbnail: video.thumbnail_url,
                    duration: video.duration,
                    viewCount: video.view_count,
                    relevanceScore: video.relevance_score
                })),
                searchQuery: response.search_query,
                totalResults: response.total_results,
                source: 'ai'
            };

        } catch (error) {
            console.error('YouTube search failed:', error);
            return this.getFallbackYouTubeResults(problemData);
        }
    }

    async detectStruggle(problemData, userContext) {
        if (!this.isOnline) {
            return this.getFallbackStruggleDetection(userContext);
        }

        try {
            const requestData = {
                problem_data: {
                    title: problemData.title,
                    slug: problemData.slug,
                    difficulty: problemData.difficulty,
                    category: problemData.category,
                    description: problemData.description,
                    constraints: problemData.constraints || [],
                    tags: problemData.tags || []
                },
                user_context: {
                    session_id: this.sessionId,
                    language: userContext.language || 'javascript',
                    previous_hints: userContext.previousHints || [],
                    time_spent: userContext.timeSpent || 0,
                    hint_count: userContext.hintCount || 0,
                    struggle_indicators: userContext.struggleIndicators || [],
                    skill_level: userContext.skillLevel || 'intermediate'
                },
                interaction_history: userContext.interactionHistory || []
            };

            const response = await this.makeRequest('/struggle-detection', requestData);

            return {
                isStruggling: response.is_struggling,
                struggleScore: response.struggle_score,
                struggleLevel: this.mapStruggleLevel(response.struggle_score),
                indicators: response.indicators || [],
                recommendations: response.recommendations || [],
                shouldSuggestYouTube: response.suggest_youtube,
                interventionNeeded: response.struggle_score > 0.7,
                encouragementMessage: this.generateEncouragement(response.struggle_score),
                nextAction: this.suggestNextAction(response)
            };

        } catch (error) {
            console.error('AI struggle detection failed:', error);
            return this.getFallbackStruggleDetection(userContext);
        }
    }

    mapStruggleLevel(score) {
        if (score >= 0.8) return 'severe';
        if (score >= 0.6) return 'moderate';
        if (score >= 0.3) return 'mild';
        return 'none';
    }

    generateEncouragement(struggleScore) {
        if (struggleScore < 0.3) {
            return "You're doing great! Keep up the excellent work.";
        } else if (struggleScore < 0.6) {
            return "You're on the right track. Take your time and think through each step.";
        } else if (struggleScore < 0.8) {
            return "This is challenging, but you can do it! Consider breaking the problem down.";
        } else {
            return "Don't give up! Every expert was once a beginner. Consider getting some guidance.";
        }
    }

    suggestNextAction(response) {
        if (response.suggest_youtube) {
            return 'watch_video';
        } else if (response.struggle_score > 0.5) {
            return 'get_hint';
        } else if (response.struggle_score > 0.3) {
            return 'review_problem';
        } else {
            return 'continue_solving';
        }
    }

    getFallbackStruggleDetection(userContext) {
        // Simple fallback logic when AI is not available
        const hintCount = userContext.hintCount || 0;
        const timeSpent = userContext.timeSpent || 0;

        let struggleScore = 0;
        const indicators = [];

        if (hintCount >= 3) {
            struggleScore += 0.4;
            indicators.push('Multiple hints requested');
        }

        if (timeSpent > 20) {
            struggleScore += 0.3;
            indicators.push('Extended time spent');
        }

        const isStruggling = struggleScore > 0.5;

        return {
            isStruggling,
            struggleScore,
            struggleLevel: this.mapStruggleLevel(struggleScore),
            indicators,
            recommendations: isStruggling ?
                ['Take a break', 'Review the problem', 'Try a different approach'] :
                ['Keep going', 'You\'re doing well'],
            shouldSuggestYouTube: struggleScore > 0.6,
            interventionNeeded: struggleScore > 0.7,
            encouragementMessage: this.generateEncouragement(struggleScore),
            nextAction: this.suggestNextAction({ struggle_score: struggleScore, suggest_youtube: struggleScore > 0.6 })
        };
    }

    async logInteraction(problemSlug, interactionType, interactionData) {
        if (!this.isOnline) return;

        try {
            const requestData = {
                session_id: this.sessionId,
                problem_slug: problemSlug,
                interaction_type: interactionType,
                interaction_data: interactionData
            };

            await this.makeRequest('/interaction', requestData);
        } catch (error) {
            console.warn('Failed to log interaction:', error);
        }
    }

    // Fallback methods when AI is not available
    getFallbackHint(problemData, level) {
        const fallbackHints = {
            gentle: "Think about what data structures might be helpful for this problem. Consider the constraints and what operations you need to perform efficiently.",
            moderate: "Consider breaking this problem down into smaller steps. What algorithm patterns have you seen that might apply here?",
            strong: "Focus on the optimal approach for this problem type. Think about time and space complexity, and consider if there are any standard algorithms that apply."
        };

        return {
            success: true,
            hint: fallbackHints[level] || fallbackHints.gentle,
            level: level,
            followUpQuestions: ["What approach would be most efficient?"],
            keyConcepts: ["Problem Analysis"],
            nextSteps: ["Break down the problem into smaller parts"],
            encouragement: "Keep thinking step by step - you can solve this!",
            source: 'fallback',
            model: 'fallback'
        };
    }

    getFallbackAnalysis(problemData) {
        return {
            success: true,
            problemType: "General Problem Solving",
            keyConcepts: ["Algorithm Design", "Data Structures"],
            optimalApproach: {
                name: "Standard Approach",
                description: "Apply appropriate algorithm based on problem constraints",
                time_complexity: "O(n)",
                space_complexity: "O(1)"
            },
            alternativeApproaches: [],
            keyInsights: ["Consider the problem constraints carefully"],
            commonPitfalls: ["Not handling edge cases"],
            edgeCases: ["Empty input", "Single element"],
            difficultyJustification: "Analysis not available",
            source: 'fallback'
        };
    }

    getFallbackYouTubeResults(problemData) {
        return {
            success: false,
            videos: [],
            searchQuery: "",
            totalResults: 0,
            source: 'fallback',
            message: "YouTube search not available. Try searching manually for: " + problemData.title + " solution"
        };
    }

    getConnectionStatus() {
        return {
            isOnline: this.isOnline,
            sessionId: this.sessionId,
            cacheSize: this.requestCache.size
        };
    }

    clearCache() {
        this.requestCache.clear();
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AIIntegrationService };
}

// Global instance for easy access
window.aiIntegrationService = new AIIntegrationService();
