// Intelligent Test Case Generation System for LeetCode Helper Pro
class TestCaseGenerator {
    constructor() {
        this.testCaseDatabase = this.initializeTestCaseDatabase();
        this.edgeCaseGenerator = new EdgeCaseGenerator();
        this.performanceTestGenerator = new PerformanceTestGenerator();
        this.validationEngine = new TestCaseValidator();
    }

    async generateTestCases(problem) {
        const testSuite = {
            basic: [],
            edge: [],
            performance: [],
            custom: [],
            metadata: {
                totalCases: 0,
                coverage: [],
                difficulty: problem.difficulty
            }
        };

        // Generate basic test cases
        testSuite.basic = await this.generateBasicTestCases(problem);
        
        // Generate edge cases
        testSuite.edge = await this.edgeCaseGenerator.generate(problem);
        
        // Generate performance test cases
        testSuite.performance = await this.performanceTestGenerator.generate(problem);
        
        // Generate custom test cases based on problem patterns
        testSuite.custom = await this.generateCustomTestCases(problem);

        // Validate all test cases
        testSuite = await this.validationEngine.validateTestSuite(testSuite, problem);

        // Update metadata
        testSuite.metadata.totalCases = this.countTotalTestCases(testSuite);
        testSuite.metadata.coverage = this.analyzeCoverage(testSuite, problem);

        return testSuite;
    }

    async generateBasicTestCases(problem) {
        const problemKey = this.getProblemKey(problem);
        
        // Check if we have predefined test cases
        if (this.testCaseDatabase[problemKey]) {
            return this.testCaseDatabase[problemKey].basic;
        }

        // Generate pattern-based basic test cases
        return this.generatePatternBasedTestCases(problem);
    }

    generatePatternBasedTestCases(problem) {
        const title = problem.title.toLowerCase();
        const testCases = [];

        // Array problems
        if (title.includes('array') || title.includes('sum')) {
            testCases.push({
                name: 'Basic Array Test',
                input: '[2, 7, 11, 15], target = 9',
                output: '[0, 1]',
                explanation: 'Standard case with solution at beginning',
                category: 'basic'
            });
            
            testCases.push({
                name: 'Middle Elements',
                input: '[3, 2, 4], target = 6',
                output: '[1, 2]',
                explanation: 'Solution in middle of array',
                category: 'basic'
            });
        }

        // String problems
        if (title.includes('string') || title.includes('substring')) {
            testCases.push({
                name: 'Basic String Test',
                input: '"abcabcbb"',
                output: '3',
                explanation: 'Standard string with repeating pattern',
                category: 'basic'
            });
            
            testCases.push({
                name: 'Short String',
                input: '"bbbbb"',
                output: '1',
                explanation: 'String with all same characters',
                category: 'basic'
            });
        }

        // Linked List problems
        if (title.includes('linked') || title.includes('list')) {
            testCases.push({
                name: 'Basic Linked List',
                input: '[2, 4, 3], [5, 6, 4]',
                output: '[7, 0, 8]',
                explanation: 'Standard linked list operation',
                category: 'basic'
            });
        }

        // Tree problems
        if (title.includes('tree') || title.includes('binary')) {
            testCases.push({
                name: 'Basic Tree Test',
                input: '[3, 9, 20, null, null, 15, 7]',
                output: '3',
                explanation: 'Standard binary tree structure',
                category: 'basic'
            });
        }

        return testCases.length > 0 ? testCases : this.getDefaultBasicTestCases();
    }

    getDefaultBasicTestCases() {
        return [
            {
                name: 'Basic Test Case',
                input: 'Standard input',
                output: 'Expected output',
                explanation: 'Basic functionality test',
                category: 'basic'
            },
            {
                name: 'Alternative Input',
                input: 'Different valid input',
                output: 'Corresponding output',
                explanation: 'Tests different input format',
                category: 'basic'
            }
        ];
    }

    async generateCustomTestCases(problem) {
        const customCases = [];
        const difficulty = problem.difficulty.toLowerCase();

        // Generate difficulty-specific test cases
        if (difficulty === 'easy') {
            customCases.push(...this.generateEasyCustomCases(problem));
        } else if (difficulty === 'medium') {
            customCases.push(...this.generateMediumCustomCases(problem));
        } else if (difficulty === 'hard') {
            customCases.push(...this.generateHardCustomCases(problem));
        }

        return customCases;
    }

    generateEasyCustomCases(problem) {
        return [
            {
                name: 'Simple Positive Case',
                input: 'Simple valid input',
                output: 'Clear expected output',
                explanation: 'Straightforward test for easy problem',
                category: 'custom'
            }
        ];
    }

    generateMediumCustomCases(problem) {
        return [
            {
                name: 'Complex Valid Case',
                input: 'More complex input',
                output: 'Detailed expected output',
                explanation: 'Tests intermediate complexity',
                category: 'custom'
            },
            {
                name: 'Multiple Solutions Case',
                input: 'Input with multiple valid answers',
                output: 'One of the valid outputs',
                explanation: 'Tests when multiple solutions exist',
                category: 'custom'
            }
        ];
    }

    generateHardCustomCases(problem) {
        return [
            {
                name: 'Complex Algorithm Test',
                input: 'Complex algorithmic input',
                output: 'Sophisticated expected output',
                explanation: 'Tests advanced algorithmic thinking',
                category: 'custom'
            },
            {
                name: 'Optimization Required',
                input: 'Input requiring optimal solution',
                output: 'Output demonstrating efficiency',
                explanation: 'Tests optimization requirements',
                category: 'custom'
            }
        ];
    }

    countTotalTestCases(testSuite) {
        return testSuite.basic.length + testSuite.edge.length + 
               testSuite.performance.length + testSuite.custom.length;
    }

    analyzeCoverage(testSuite, problem) {
        const coverage = [];
        
        if (testSuite.basic.length > 0) coverage.push('Basic functionality');
        if (testSuite.edge.length > 0) coverage.push('Edge cases');
        if (testSuite.performance.length > 0) coverage.push('Performance limits');
        if (testSuite.custom.length > 0) coverage.push('Problem-specific scenarios');
        
        return coverage;
    }

    getProblemKey(problem) {
        return problem.slug || problem.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    initializeTestCaseDatabase() {
        return {
            'two-sum': {
                basic: [
                    {
                        name: 'Basic Two Sum',
                        input: 'nums = [2,7,11,15], target = 9',
                        output: '[0,1]',
                        explanation: 'nums[0] + nums[1] = 2 + 7 = 9',
                        category: 'basic'
                    },
                    {
                        name: 'Different Positions',
                        input: 'nums = [3,2,4], target = 6',
                        output: '[1,2]',
                        explanation: 'nums[1] + nums[2] = 2 + 4 = 6',
                        category: 'basic'
                    }
                ]
            },
            'add-two-numbers': {
                basic: [
                    {
                        name: 'Basic Addition',
                        input: 'l1 = [2,4,3], l2 = [5,6,4]',
                        output: '[7,0,8]',
                        explanation: '342 + 465 = 807',
                        category: 'basic'
                    },
                    {
                        name: 'Single Digits',
                        input: 'l1 = [0], l2 = [0]',
                        output: '[0]',
                        explanation: '0 + 0 = 0',
                        category: 'basic'
                    }
                ]
            },
            'longest-substring-without-repeating-characters': {
                basic: [
                    {
                        name: 'Mixed Characters',
                        input: 's = "abcabcbb"',
                        output: '3',
                        explanation: 'The answer is "abc", with length 3',
                        category: 'basic'
                    },
                    {
                        name: 'Repeating Character',
                        input: 's = "bbbbb"',
                        output: '1',
                        explanation: 'The answer is "b", with length 1',
                        category: 'basic'
                    }
                ]
            }
        };
    }
}

// Edge Case Generation System
class EdgeCaseGenerator {
    async generate(problem) {
        const edgeCases = [];
        
        // Common edge cases for all problems
        edgeCases.push(...this.generateCommonEdgeCases(problem));
        
        // Type-specific edge cases
        edgeCases.push(...this.generateTypeSpecificEdgeCases(problem));
        
        // Problem-specific edge cases
        edgeCases.push(...this.generateProblemSpecificEdgeCases(problem));

        return edgeCases;
    }

    generateCommonEdgeCases(problem) {
        return [
            {
                name: 'Empty Input',
                input: 'Empty or null input',
                output: 'Appropriate empty response',
                explanation: 'Tests handling of empty input',
                category: 'edge'
            },
            {
                name: 'Single Element',
                input: 'Input with single element',
                output: 'Single element response',
                explanation: 'Tests minimum valid input',
                category: 'edge'
            }
        ];
    }

    generateTypeSpecificEdgeCases(problem) {
        const title = problem.title.toLowerCase();
        const edgeCases = [];

        if (title.includes('array')) {
            edgeCases.push(
                {
                    name: 'Large Array',
                    input: 'Array with maximum allowed size',
                    output: 'Correct output for large input',
                    explanation: 'Tests performance with large arrays',
                    category: 'edge'
                },
                {
                    name: 'Duplicate Elements',
                    input: 'Array with all duplicate elements',
                    output: 'Handles duplicates correctly',
                    explanation: 'Tests duplicate handling',
                    category: 'edge'
                }
            );
        }

        if (title.includes('string')) {
            edgeCases.push(
                {
                    name: 'Very Long String',
                    input: 'String at maximum length limit',
                    output: 'Correct processing of long string',
                    explanation: 'Tests string length limits',
                    category: 'edge'
                },
                {
                    name: 'Special Characters',
                    input: 'String with special characters',
                    output: 'Proper handling of special chars',
                    explanation: 'Tests character set handling',
                    category: 'edge'
                }
            );
        }

        return edgeCases;
    }

    generateProblemSpecificEdgeCases(problem) {
        const problemKey = problem.slug || problem.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
        
        const specificEdgeCases = {
            'two-sum': [
                {
                    name: 'Same Number Twice',
                    input: 'nums = [3,3], target = 6',
                    output: '[0,1]',
                    explanation: 'Uses same value from different indices',
                    category: 'edge'
                }
            ],
            'valid-parentheses': [
                {
                    name: 'Only Opening Brackets',
                    input: 's = "((("',
                    output: 'false',
                    explanation: 'No closing brackets to match',
                    category: 'edge'
                },
                {
                    name: 'Only Closing Brackets',
                    input: 's = ")))"',
                    output: 'false',
                    explanation: 'No opening brackets to match',
                    category: 'edge'
                }
            ]
        };

        return specificEdgeCases[problemKey] || [];
    }
}

// Performance Test Generation System
class PerformanceTestGenerator {
    async generate(problem) {
        const performanceTests = [];
        
        // Generate stress tests
        performanceTests.push(...this.generateStressTests(problem));
        
        // Generate boundary tests
        performanceTests.push(...this.generateBoundaryTests(problem));
        
        // Generate time complexity tests
        performanceTests.push(...this.generateTimeComplexityTests(problem));

        return performanceTests;
    }

    generateStressTests(problem) {
        return [
            {
                name: 'Maximum Input Size',
                input: 'Input at maximum constraint limit',
                output: 'Expected output for max input',
                explanation: 'Tests algorithm with largest possible input',
                category: 'performance',
                expectedTime: 'Should complete within time limit'
            }
        ];
    }

    generateBoundaryTests(problem) {
        return [
            {
                name: 'Minimum Constraint',
                input: 'Input at minimum constraint boundary',
                output: 'Output for minimum input',
                explanation: 'Tests lower boundary conditions',
                category: 'performance'
            },
            {
                name: 'Maximum Constraint',
                input: 'Input at maximum constraint boundary',
                output: 'Output for maximum input',
                explanation: 'Tests upper boundary conditions',
                category: 'performance'
            }
        ];
    }

    generateTimeComplexityTests(problem) {
        return [
            {
                name: 'Worst Case Scenario',
                input: 'Input that triggers worst-case time complexity',
                output: 'Output for worst-case input',
                explanation: 'Tests algorithm under worst conditions',
                category: 'performance',
                complexity: 'Worst-case time complexity'
            }
        ];
    }
}

// Test Case Validation System
class TestCaseValidator {
    async validateTestSuite(testSuite, problem) {
        // Validate each category of test cases
        testSuite.basic = await this.validateTestCases(testSuite.basic, 'basic');
        testSuite.edge = await this.validateTestCases(testSuite.edge, 'edge');
        testSuite.performance = await this.validateTestCases(testSuite.performance, 'performance');
        testSuite.custom = await this.validateTestCases(testSuite.custom, 'custom');

        return testSuite;
    }

    async validateTestCases(testCases, category) {
        return testCases.filter(testCase => {
            return this.isValidTestCase(testCase) && this.hasRequiredFields(testCase);
        });
    }

    isValidTestCase(testCase) {
        return testCase.name && testCase.input && testCase.output && testCase.explanation;
    }

    hasRequiredFields(testCase) {
        const requiredFields = ['name', 'input', 'output', 'explanation', 'category'];
        return requiredFields.every(field => testCase.hasOwnProperty(field));
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        TestCaseGenerator, 
        EdgeCaseGenerator, 
        PerformanceTestGenerator, 
        TestCaseValidator 
    };
}
