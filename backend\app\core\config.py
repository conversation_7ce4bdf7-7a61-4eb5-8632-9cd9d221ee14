"""
Configuration settings for LeetCode Helper Pro AI Backend
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic app settings
    APP_NAME: str = "LeetCode Helper Pro AI Backend"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="localhost", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "chrome-extension://*"],
        env="ALLOWED_ORIGINS"
    )
    
    # AI API Keys
    GOOGLE_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_API_KEY")
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    
    # YouTube API
    YOUTUBE_API_KEY: Optional[str] = Field(default=None, env="YOUTUBE_API_KEY")
    
    # Database settings
    DATABASE_URL: str = Field(
        default="sqlite:///./leetcode_helper.db",
        env="DATABASE_URL"
    )
    
    # Redis settings (for caching)
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_ENABLED: bool = Field(default=False, env="REDIS_ENABLED")
    
    # AI Model settings
    DEFAULT_AI_MODEL: str = Field(default="gemini-pro", env="DEFAULT_AI_MODEL")
    MAX_TOKENS: int = Field(default=1000, env="MAX_TOKENS")
    TEMPERATURE: float = Field(default=0.7, env="TEMPERATURE")
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Security
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    
    # Feature flags
    ENABLE_YOUTUBE_SEARCH: bool = Field(default=True, env="ENABLE_YOUTUBE_SEARCH")
    ENABLE_AI_HINTS: bool = Field(default=True, env="ENABLE_AI_HINTS")
    ENABLE_PROBLEM_ANALYSIS: bool = Field(default=True, env="ENABLE_PROBLEM_ANALYSIS")
    
    # Cache settings
    CACHE_TTL_SECONDS: int = Field(default=3600, env="CACHE_TTL_SECONDS")  # 1 hour
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # AI Prompt settings
    HINT_SYSTEM_PROMPT: str = Field(
        default="""You are an expert programming tutor helping students learn to solve coding problems. 
        Your goal is to guide students toward the solution without giving direct code. 
        Provide hints that encourage thinking and learning.""",
        env="HINT_SYSTEM_PROMPT"
    )
    
    ANALYSIS_SYSTEM_PROMPT: str = Field(
        default="""You are an expert algorithm analyst. Analyze the given coding problem and provide 
        insights about the optimal approach, time/space complexity, and key concepts involved.""",
        env="ANALYSIS_SYSTEM_PROMPT"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Create global settings instance
settings = Settings()


# Validation functions
def validate_api_keys():
    """Validate that required API keys are present"""
    missing_keys = []
    
    if not settings.GOOGLE_API_KEY and settings.ENABLE_AI_HINTS:
        missing_keys.append("GOOGLE_API_KEY")
    
    if not settings.YOUTUBE_API_KEY and settings.ENABLE_YOUTUBE_SEARCH:
        missing_keys.append("YOUTUBE_API_KEY")
    
    if missing_keys:
        raise ValueError(f"Missing required API keys: {', '.join(missing_keys)}")


def get_ai_config():
    """Get AI configuration dictionary"""
    return {
        "model": settings.DEFAULT_AI_MODEL,
        "max_tokens": settings.MAX_TOKENS,
        "temperature": settings.TEMPERATURE,
        "api_key": settings.GOOGLE_API_KEY
    }


def get_youtube_config():
    """Get YouTube configuration dictionary"""
    return {
        "api_key": settings.YOUTUBE_API_KEY,
        "enabled": settings.ENABLE_YOUTUBE_SEARCH
    }
