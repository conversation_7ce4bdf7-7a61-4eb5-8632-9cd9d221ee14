"""
AI Service using Google Gemini API for LeetCode Helper Pro
"""

import google.generativeai as genai
from typing import Dict, List, Optional, Any
import json
import asyncio
import time
from datetime import datetime
import logging

from app.core.config import settings
from app.models.schemas import ProblemData, HintRequest, AnalysisRequest, StruggleDetectionRequest

logger = logging.getLogger(__name__)


class GeminiAIService:
    """Google Gemini AI service for problem analysis and hint generation"""
    
    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.request_count = 0
        self.last_request_time = 0
        
    async def initialize(self):
        """Initialize Gemini AI service"""
        try:
            if not settings.GOOGLE_API_KEY:
                raise ValueError("GOOGLE_API_KEY not found in environment variables")
            
            # Configure Gemini
            genai.configure(api_key=settings.GOOGLE_API_KEY)
            
            # Initialize the model
            self.model = genai.GenerativeModel('gemini-pro')
            
            # Test the connection
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("Gemini AI service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI service: {e}")
            raise
    
    async def _test_connection(self):
        """Test Gemini API connection"""
        try:
            test_prompt = "Hello, this is a test. Please respond with 'Connection successful'."
            response = await self._generate_response(test_prompt)
            
            if "successful" not in response.lower():
                raise Exception("Unexpected test response from Gemini")
                
            logger.info("Gemini API connection test successful")
            
        except Exception as e:
            logger.error(f"Gemini API connection test failed: {e}")
            raise
    
    async def _generate_response(self, prompt: str, max_retries: int = 3) -> str:
        """Generate response from Gemini with rate limiting and retries"""
        
        # Rate limiting
        await self._rate_limit()
        
        for attempt in range(max_retries):
            try:
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=settings.MAX_TOKENS,
                        temperature=settings.TEMPERATURE,
                    )
                )
                
                if response.text:
                    self.request_count += 1
                    return response.text.strip()
                else:
                    raise Exception("Empty response from Gemini")
                    
            except Exception as e:
                logger.warning(f"Gemini API attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise Exception("All Gemini API attempts failed")
    
    async def _rate_limit(self):
        """Implement rate limiting for Gemini API"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # Ensure at least 1 second between requests
        if time_since_last < 1.0:
            await asyncio.sleep(1.0 - time_since_last)
        
        self.last_request_time = time.time()
    
    async def analyze_problem(self, problem_data: ProblemData) -> Dict[str, Any]:
        """Analyze a LeetCode problem using Gemini"""
        
        if not self.is_initialized:
            raise Exception("AI service not initialized")
        
        analysis_prompt = self._create_analysis_prompt(problem_data)
        
        try:
            response = await self._generate_response(analysis_prompt)
            analysis = self._parse_analysis_response(response)
            
            logger.info(f"Successfully analyzed problem: {problem_data.title}")
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze problem {problem_data.title}: {e}")
            raise
    
    def _create_analysis_prompt(self, problem_data: ProblemData) -> str:
        """Create analysis prompt for Gemini"""
        
        prompt = f"""
{settings.ANALYSIS_SYSTEM_PROMPT}

Please analyze this LeetCode problem and provide a comprehensive analysis:

**Problem Title:** {problem_data.title}
**Difficulty:** {problem_data.difficulty}
**Category:** {problem_data.category}

**Problem Description:**
{problem_data.description}

**Constraints:**
{chr(10).join(problem_data.constraints) if problem_data.constraints else 'Not specified'}

Please provide your analysis in the following JSON format:

{{
    "problem_type": "string (e.g., 'Two Pointers', 'Dynamic Programming', etc.)",
    "key_concepts": ["concept1", "concept2", "concept3"],
    "optimal_approach": {{
        "name": "string",
        "description": "string",
        "time_complexity": "string",
        "space_complexity": "string"
    }},
    "alternative_approaches": [
        {{
            "name": "string",
            "description": "string", 
            "time_complexity": "string",
            "space_complexity": "string",
            "pros": ["pro1", "pro2"],
            "cons": ["con1", "con2"]
        }}
    ],
    "key_insights": ["insight1", "insight2", "insight3"],
    "common_pitfalls": ["pitfall1", "pitfall2"],
    "edge_cases": ["edge_case1", "edge_case2"],
    "difficulty_justification": "string explaining why this problem has this difficulty level"
}}

Ensure your response is valid JSON and focuses on educational guidance rather than direct code solutions.
"""
        return prompt
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse Gemini analysis response"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[start_idx:end_idx]
            analysis = json.loads(json_str)
            
            # Validate required fields
            required_fields = ['problem_type', 'key_concepts', 'optimal_approach']
            for field in required_fields:
                if field not in analysis:
                    raise ValueError(f"Missing required field: {field}")
            
            return analysis
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response content: {response}")
            
            # Return fallback analysis
            return self._create_fallback_analysis()
        
        except Exception as e:
            logger.error(f"Error parsing analysis response: {e}")
            return self._create_fallback_analysis()
    
    def _create_fallback_analysis(self) -> Dict[str, Any]:
        """Create fallback analysis when parsing fails"""
        return {
            "problem_type": "General Problem Solving",
            "key_concepts": ["Algorithm Design", "Data Structures"],
            "optimal_approach": {
                "name": "Standard Approach",
                "description": "Apply appropriate algorithm based on problem constraints",
                "time_complexity": "O(n)",
                "space_complexity": "O(1)"
            },
            "alternative_approaches": [],
            "key_insights": ["Consider the problem constraints carefully"],
            "common_pitfalls": ["Not handling edge cases"],
            "edge_cases": ["Empty input", "Single element"],
            "difficulty_justification": "Analysis not available"
        }
    
    async def generate_hint(self, hint_request: HintRequest) -> Dict[str, Any]:
        """Generate progressive hint using Gemini"""
        
        if not self.is_initialized:
            raise Exception("AI service not initialized")
        
        hint_prompt = self._create_hint_prompt(hint_request)
        
        try:
            response = await self._generate_response(hint_prompt)
            hint = self._parse_hint_response(response, hint_request.level)
            
            logger.info(f"Generated {hint_request.level} hint for {hint_request.problem_data.title}")
            return hint
            
        except Exception as e:
            logger.error(f"Failed to generate hint: {e}")
            raise
    
    def _create_hint_prompt(self, hint_request: HintRequest) -> str:
        """Create hint generation prompt for Gemini"""
        
        level_instructions = {
            "gentle": """
            Provide a VERY SUBTLE hint that nudges the student in the right direction without revealing the solution.
            Focus on asking guiding questions or pointing out key observations about the problem.
            Do NOT mention specific algorithms, data structures, or implementation details.
            """,
            "moderate": """
            Provide a MODERATE hint that gives clearer direction while still requiring the student to think.
            You can mention general approaches or data structures but avoid specific implementation details.
            Guide them toward the right algorithmic thinking.
            """,
            "strong": """
            Provide a DETAILED hint that explains the approach and key steps without giving actual code.
            You can mention specific algorithms, data structures, and the general solution strategy.
            Explain the reasoning behind the approach but let them implement it.
            """
        }
        
        context_info = ""
        if hint_request.user_context:
            if hint_request.user_context.previous_hints:
                context_info += f"\nPrevious hints given: {len(hint_request.user_context.previous_hints)}"
            if hint_request.user_context.time_spent:
                context_info += f"\nTime spent on problem: {hint_request.user_context.time_spent} minutes"
            if hint_request.user_context.language:
                context_info += f"\nPreferred language: {hint_request.user_context.language}"
        
        prompt = f"""
{settings.HINT_SYSTEM_PROMPT}

{level_instructions[hint_request.level]}

**Problem Title:** {hint_request.problem_data.title}
**Difficulty:** {hint_request.problem_data.difficulty}
**Category:** {hint_request.problem_data.category}

**Problem Description:**
{hint_request.problem_data.description}

**Hint Level Requested:** {hint_request.level.upper()}

{context_info}

Please provide your hint in the following JSON format:

{{
    "hint_text": "Your educational hint here",
    "follow_up_questions": ["question1", "question2"],
    "key_concepts": ["concept1", "concept2"],
    "next_steps": ["step1", "step2"],
    "encouragement": "Encouraging message for the student"
}}

Remember: 
- NEVER provide actual code
- Focus on teaching and guiding
- Encourage independent thinking
- Be supportive and educational
"""
        return prompt
    
    def _parse_hint_response(self, response: str, level: str) -> Dict[str, Any]:
        """Parse Gemini hint response"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[start_idx:end_idx]
            hint = json.loads(json_str)
            
            # Validate required fields
            if 'hint_text' not in hint:
                raise ValueError("Missing hint_text field")
            
            # Add metadata
            hint['level'] = level
            hint['generated_at'] = datetime.utcnow().isoformat()
            hint['model'] = 'gemini-pro'
            
            return hint
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse hint JSON response: {e}")
            
            # Return fallback hint
            return self._create_fallback_hint(response, level)
        
        except Exception as e:
            logger.error(f"Error parsing hint response: {e}")
            return self._create_fallback_hint(response, level)
    
    def _create_fallback_hint(self, response: str, level: str) -> Dict[str, Any]:
        """Create fallback hint when parsing fails"""
        
        # Try to extract meaningful text from response
        hint_text = response[:500] if response else "Consider the problem constraints and think about efficient approaches."
        
        return {
            "hint_text": hint_text,
            "follow_up_questions": ["What data structures might be helpful here?"],
            "key_concepts": ["Problem Analysis"],
            "next_steps": ["Break down the problem into smaller parts"],
            "encouragement": "You're on the right track! Keep thinking step by step.",
            "level": level,
            "generated_at": datetime.utcnow().isoformat(),
            "model": "gemini-pro"
        }
    
    def is_ready(self) -> bool:
        """Check if AI service is ready"""
        return self.is_initialized and self.model is not None
    
    async def cleanup(self):
        """Cleanup AI service resources"""
        self.is_initialized = False
        self.model = None
        logger.info("AI service cleaned up")
    
    async def analyze_user_struggle(self, struggle_request: StruggleDetectionRequest) -> Dict[str, Any]:
        """Use AI to analyze user struggle patterns and provide insights"""

        if not self.is_initialized:
            raise Exception("AI service not initialized")

        struggle_prompt = self._create_struggle_analysis_prompt(struggle_request)

        try:
            response = await self._generate_response(struggle_prompt)
            analysis = self._parse_struggle_response(response)

            logger.info(f"AI struggle analysis completed for {struggle_request.problem_data.title}")
            return analysis

        except Exception as e:
            logger.error(f"Failed to analyze user struggle: {e}")
            raise

    def _create_struggle_analysis_prompt(self, struggle_request: StruggleDetectionRequest) -> str:
        """Create prompt for AI-powered struggle analysis"""

        user_context = struggle_request.user_context
        problem_data = struggle_request.problem_data

        prompt = f"""
You are an expert programming tutor analyzing a student's learning patterns and struggle indicators.

**Problem Information:**
- Title: {problem_data.title}
- Difficulty: {problem_data.difficulty}
- Category: {problem_data.category}

**Student Context:**
- Hints requested: {user_context.hint_count or 0}
- Time spent: {user_context.time_spent or 0} minutes
- Programming language: {user_context.language or 'Not specified'}
- Previous hints: {len(user_context.previous_hints or [])}
- Struggle indicators: {', '.join(user_context.struggle_indicators or ['None'])}

**Interaction History:**
{len(struggle_request.interaction_history or [])} recent interactions

Please analyze this student's situation and provide insights in the following JSON format:

{{
    "struggle_level": "none|mild|moderate|severe",
    "confidence_score": 0.0-1.0,
    "primary_challenges": ["challenge1", "challenge2"],
    "learning_recommendations": ["recommendation1", "recommendation2"],
    "intervention_needed": true/false,
    "suggested_resources": ["resource1", "resource2"],
    "encouragement_message": "Personalized encouraging message",
    "next_best_action": "Specific next step recommendation"
}}

Focus on educational guidance and student support. Be encouraging while being realistic about the challenges.
"""
        return prompt

    def _parse_struggle_response(self, response: str) -> Dict[str, Any]:
        """Parse AI struggle analysis response"""
        try:
            # Try to extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")

            json_str = response[start_idx:end_idx]
            analysis = json.loads(json_str)

            # Validate required fields
            required_fields = ['struggle_level', 'confidence_score', 'intervention_needed']
            for field in required_fields:
                if field not in analysis:
                    raise ValueError(f"Missing required field: {field}")

            return analysis

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse struggle analysis JSON: {e}")
            return self._create_fallback_struggle_analysis()

        except Exception as e:
            logger.error(f"Error parsing struggle analysis: {e}")
            return self._create_fallback_struggle_analysis()

    def _create_fallback_struggle_analysis(self) -> Dict[str, Any]:
        """Create fallback struggle analysis when AI parsing fails"""
        return {
            "struggle_level": "mild",
            "confidence_score": 0.5,
            "primary_challenges": ["Problem complexity", "Time management"],
            "learning_recommendations": ["Break down the problem", "Practice similar problems"],
            "intervention_needed": False,
            "suggested_resources": ["Review fundamentals", "Practice exercises"],
            "encouragement_message": "Keep working step by step - you're making progress!",
            "next_best_action": "Focus on understanding the problem requirements"
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get AI service statistics"""
        return {
            "initialized": self.is_initialized,
            "total_requests": self.request_count,
            "model": "gemini-pro",
            "last_request_time": self.last_request_time
        }


# Create global AI service instance
AIService = GeminiAIService
