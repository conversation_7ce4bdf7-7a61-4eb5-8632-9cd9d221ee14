"""
YouTube Service for finding LeetCode solution videos
"""

from googleapiclient.discovery import build
from googleapiclient.errors import <PERSON>ttp<PERSON><PERSON>r
from typing import List, Dict, Any, Optional
import asyncio
import logging
import re
from datetime import datetime, timedelta
import json

from app.core.config import settings
from app.models.schemas import ProblemData, YouTubeVideo, YouTubeRequest, YouTubeResponse

logger = logging.getLogger(__name__)


class YouTubeService:
    """Service for finding relevant YouTube solution videos"""
    
    def __init__(self):
        self.youtube_client = None
        self.is_initialized = False
        self.cache = {}  # Simple in-memory cache
        self.request_count = 0
        
    async def initialize(self):
        """Initialize YouTube service"""
        try:
            if not settings.YOUTUBE_API_KEY:
                logger.warning("YOUTUBE_API_KEY not found, YouTube service will be disabled")
                return
            
            # Initialize YouTube API client
            self.youtube_client = build(
                'youtube', 'v3',
                developerKey=settings.YOUTUBE_API_KEY
            )
            
            # Test the connection
            await self._test_connection()
            
            self.is_initialized = True
            logger.info("YouTube service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize YouTube service: {e}")
            # Don't raise - service can work without YouTube
    
    async def _test_connection(self):
        """Test YouTube API connection"""
        try:
            # Simple search test
            request = self.youtube_client.search().list(
                part='snippet',
                q='leetcode',
                maxResults=1,
                type='video'
            )
            
            response = request.execute()
            
            if not response.get('items'):
                raise Exception("No results from YouTube API test")
                
            logger.info("YouTube API connection test successful")
            
        except Exception as e:
            logger.error(f"YouTube API connection test failed: {e}")
            raise
    
    async def find_solution_videos(self, youtube_request: YouTubeRequest) -> YouTubeResponse:
        """Find YouTube videos for a LeetCode problem"""
        
        if not self.is_initialized:
            logger.warning("YouTube service not initialized, returning empty results")
            return YouTubeResponse(
                videos=[],
                search_query="",
                total_results=0
            )
        
        try:
            # Generate search queries
            search_queries = self._generate_search_queries(youtube_request.problem_data)
            
            all_videos = []
            total_results = 0
            
            # Search with multiple queries to get diverse results
            for query in search_queries[:3]:  # Limit to 3 queries to avoid quota issues
                videos = await self._search_videos(query, youtube_request.max_results // len(search_queries) + 1)
                all_videos.extend(videos)
                total_results += len(videos)
            
            # Remove duplicates and rank by relevance
            unique_videos = self._deduplicate_videos(all_videos)
            ranked_videos = self._rank_videos(unique_videos, youtube_request.problem_data)
            
            # Limit to requested number of results
            final_videos = ranked_videos[:youtube_request.max_results]
            
            # Cache results
            cache_key = f"{youtube_request.problem_data.slug}_{youtube_request.max_results}"
            self.cache[cache_key] = {
                'videos': final_videos,
                'timestamp': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(hours=24)
            }
            
            logger.info(f"Found {len(final_videos)} YouTube videos for {youtube_request.problem_data.title}")
            
            return YouTubeResponse(
                videos=final_videos,
                search_query=search_queries[0] if search_queries else "",
                total_results=total_results
            )
            
        except Exception as e:
            logger.error(f"Failed to find YouTube videos: {e}")
            return YouTubeResponse(
                videos=[],
                search_query="",
                total_results=0
            )
    
    def _generate_search_queries(self, problem_data: ProblemData) -> List[str]:
        """Generate search queries for the problem"""
        
        # Clean problem title for search
        clean_title = re.sub(r'[^\w\s]', '', problem_data.title)
        
        queries = [
            f"leetcode {clean_title} solution explanation",
            f"leetcode {problem_data.slug} solution",
            f"{clean_title} algorithm explanation",
            f"leetcode {clean_title} {problem_data.difficulty.lower()}",
        ]
        
        # Add category-specific queries
        if problem_data.category:
            queries.append(f"leetcode {problem_data.category.lower()} {clean_title}")
        
        # Add language-specific queries if available
        popular_languages = ['python', 'java', 'javascript', 'cpp']
        for lang in popular_languages[:2]:  # Limit to avoid too many queries
            queries.append(f"leetcode {clean_title} {lang} solution")
        
        return queries
    
    async def _search_videos(self, query: str, max_results: int = 10) -> List[YouTubeVideo]:
        """Search for videos using YouTube API"""
        
        try:
            # Check cache first
            cache_key = f"search_{query}_{max_results}"
            if cache_key in self.cache:
                cached = self.cache[cache_key]
                if datetime.utcnow() < cached['expires_at']:
                    return cached['videos']
            
            # Search for videos
            search_request = self.youtube_client.search().list(
                part='snippet',
                q=query,
                maxResults=max_results,
                type='video',
                order='relevance',
                videoDuration='medium',  # 4-20 minutes
                videoDefinition='any'
            )
            
            search_response = search_request.execute()
            
            videos = []
            video_ids = []
            
            # Extract video information
            for item in search_response.get('items', []):
                video_id = item['id']['videoId']
                video_ids.append(video_id)
                
                snippet = item['snippet']
                
                video = YouTubeVideo(
                    video_id=video_id,
                    title=snippet['title'],
                    channel_name=snippet['channelTitle'],
                    url=f"https://www.youtube.com/watch?v={video_id}",
                    thumbnail_url=snippet['thumbnails'].get('medium', {}).get('url'),
                    published_at=datetime.fromisoformat(snippet['publishedAt'].replace('Z', '+00:00'))
                )
                
                videos.append(video)
            
            # Get additional video details (duration, view count, etc.)
            if video_ids:
                videos = await self._enrich_video_details(videos, video_ids)
            
            # Cache results
            self.cache[cache_key] = {
                'videos': videos,
                'timestamp': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(hours=6)
            }
            
            self.request_count += 1
            return videos
            
        except HttpError as e:
            logger.error(f"YouTube API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error searching YouTube videos: {e}")
            return []
    
    async def _enrich_video_details(self, videos: List[YouTubeVideo], video_ids: List[str]) -> List[YouTubeVideo]:
        """Enrich videos with additional details from YouTube API"""
        
        try:
            # Get video statistics and content details
            details_request = self.youtube_client.videos().list(
                part='statistics,contentDetails',
                id=','.join(video_ids)
            )
            
            details_response = details_request.execute()
            
            # Create lookup dictionary
            details_lookup = {}
            for item in details_response.get('items', []):
                video_id = item['id']
                statistics = item.get('statistics', {})
                content_details = item.get('contentDetails', {})
                
                details_lookup[video_id] = {
                    'view_count': int(statistics.get('viewCount', 0)),
                    'like_count': int(statistics.get('likeCount', 0)),
                    'duration': self._parse_duration(content_details.get('duration', ''))
                }
            
            # Enrich video objects
            for video in videos:
                if video.video_id in details_lookup:
                    details = details_lookup[video.video_id]
                    video.view_count = details['view_count']
                    video.like_count = details['like_count']
                    video.duration = details['duration']
            
            return videos
            
        except Exception as e:
            logger.error(f"Error enriching video details: {e}")
            return videos
    
    def _parse_duration(self, duration_str: str) -> str:
        """Parse YouTube duration format (PT4M13S) to readable format"""
        
        if not duration_str:
            return "Unknown"
        
        try:
            # Remove PT prefix
            duration = duration_str[2:]
            
            # Extract hours, minutes, seconds
            hours = 0
            minutes = 0
            seconds = 0
            
            if 'H' in duration:
                hours = int(duration.split('H')[0])
                duration = duration.split('H')[1]
            
            if 'M' in duration:
                minutes = int(duration.split('M')[0])
                duration = duration.split('M')[1]
            
            if 'S' in duration:
                seconds = int(duration.split('S')[0])
            
            # Format duration
            if hours > 0:
                return f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                return f"{minutes}:{seconds:02d}"
                
        except Exception:
            return "Unknown"
    
    def _deduplicate_videos(self, videos: List[YouTubeVideo]) -> List[YouTubeVideo]:
        """Remove duplicate videos based on video ID"""
        
        seen_ids = set()
        unique_videos = []
        
        for video in videos:
            if video.video_id not in seen_ids:
                seen_ids.add(video.video_id)
                unique_videos.append(video)
        
        return unique_videos
    
    def _rank_videos(self, videos: List[YouTubeVideo], problem_data: ProblemData) -> List[YouTubeVideo]:
        """Rank videos by relevance to the problem"""
        
        def calculate_relevance_score(video: YouTubeVideo) -> float:
            score = 0.0
            
            title_lower = video.title.lower()
            problem_title_lower = problem_data.title.lower()
            
            # Title relevance (40% weight)
            if problem_data.slug in title_lower:
                score += 0.4
            elif problem_title_lower in title_lower:
                score += 0.3
            elif any(word in title_lower for word in problem_title_lower.split()):
                score += 0.2
            
            # Keywords relevance (30% weight)
            keywords = ['leetcode', 'solution', 'explanation', 'algorithm']
            keyword_score = sum(0.075 for keyword in keywords if keyword in title_lower)
            score += min(keyword_score, 0.3)
            
            # Channel quality indicators (20% weight)
            if video.view_count and video.view_count > 1000:
                score += 0.1
            if video.like_count and video.like_count > 50:
                score += 0.05
            if 'leetcode' in video.channel_name.lower():
                score += 0.05
            
            # Duration preference (10% weight)
            if video.duration:
                try:
                    # Prefer videos between 5-20 minutes
                    parts = video.duration.split(':')
                    if len(parts) == 2:  # MM:SS format
                        minutes = int(parts[0])
                        if 5 <= minutes <= 20:
                            score += 0.1
                        elif 3 <= minutes <= 25:
                            score += 0.05
                except:
                    pass
            
            return min(score, 1.0)  # Cap at 1.0
        
        # Calculate relevance scores
        for video in videos:
            video.relevance_score = calculate_relevance_score(video)
        
        # Sort by relevance score (descending)
        return sorted(videos, key=lambda v: v.relevance_score, reverse=True)
    
    def is_ready(self) -> bool:
        """Check if YouTube service is ready"""
        return self.is_initialized and self.youtube_client is not None
    
    async def cleanup(self):
        """Cleanup YouTube service resources"""
        self.is_initialized = False
        self.youtube_client = None
        self.cache.clear()
        logger.info("YouTube service cleaned up")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get YouTube service statistics"""
        return {
            "initialized": self.is_initialized,
            "total_requests": self.request_count,
            "cache_size": len(self.cache),
            "api_available": settings.YOUTUBE_API_KEY is not None
        }
