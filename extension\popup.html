<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeetCode Helper Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">🧠</div>
                <h1>LeetCode Helper Pro</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <div class="status-dot"></div>
                <span id="statusText">Ready</span>
            </div>
        </header>

        <!-- Problem Detection Section -->
        <section class="problem-section" id="problemSection">
            <div class="problem-header">
                <h2 id="problemTitle">No Problem Detected</h2>
                <div class="difficulty-badge" id="difficultyBadge">
                    <span id="difficultyText">-</span>
                </div>
            </div>
            <div class="problem-meta" id="problemMeta">
                <div class="meta-item">
                    <span class="meta-label">Category:</span>
                    <span id="problemCategory">-</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Acceptance:</span>
                    <span id="problemAcceptance">-</span>
                </div>
            </div>
        </section>

        <!-- Hint System -->
        <section class="hints-section">
            <h3>Smart Hints</h3>
            <div class="hint-levels">
                <button class="hint-btn gentle" id="gentleHint" data-level="gentle">
                    <div class="hint-icon">💡</div>
                    <div class="hint-content">
                        <span class="hint-title">Gentle Hint</span>
                        <span class="hint-desc">Subtle guidance</span>
                    </div>
                </button>
                <button class="hint-btn moderate" id="moderateHint" data-level="moderate">
                    <div class="hint-icon">🔍</div>
                    <div class="hint-content">
                        <span class="hint-title">Moderate Hint</span>
                        <span class="hint-desc">Clear direction</span>
                    </div>
                </button>
                <button class="hint-btn strong" id="strongHint" data-level="strong">
                    <div class="hint-icon">🎯</div>
                    <div class="hint-content">
                        <span class="hint-title">Strong Hint</span>
                        <span class="hint-desc">Detailed approach</span>
                    </div>
                </button>
            </div>
            <div class="hint-display" id="hintDisplay">
                <div class="hint-placeholder">
                    Click a hint level to get started
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="feature-grid">
                <button class="feature-btn" id="approachBtn">
                    <div class="feature-icon">📊</div>
                    <span>Approach Analysis</span>
                </button>
                <button class="feature-btn" id="testCaseBtn">
                    <div class="feature-icon">🧪</div>
                    <span>Test Cases</span>
                </button>
                <button class="feature-btn" id="progressBtn">
                    <div class="feature-icon">📈</div>
                    <span>Progress</span>
                </button>
                <button class="feature-btn" id="settingsBtn">
                    <div class="feature-icon">⚙️</div>
                    <span>Settings</span>
                </button>
            </div>
        </section>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">Analyzing problem...</div>
        </div>

        <!-- Modal for detailed views -->
        <div class="modal" id="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Title</h3>
                    <button class="modal-close" id="modalClose">&times;</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Dynamic content -->
                </div>
            </div>
        </div>
    </div>

    <script src="mock-data.js"></script>
    <script src="ai-integration.js"></script>
    <script src="popup.js"></script>
</body>
</html>
