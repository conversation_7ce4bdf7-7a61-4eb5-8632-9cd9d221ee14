// Advanced 3-Level Hint System for LeetCode Helper Pro
class AdvancedHintSystem {
    constructor() {
        this.hintDatabase = this.initializeHintDatabase();
        this.patternRecognizer = new ProblemPatternRecognizer();
        this.hintHistory = new Map();
    }

    async generateHint(problem, level, userContext = {}) {
        const problemKey = this.getProblemKey(problem);
        const pattern = this.patternRecognizer.identifyPattern(problem);
        
        // Check if we have specific hints for this problem
        if (this.hintDatabase[problemKey]) {
            return this.getSpecificHint(problemKey, level, userContext);
        }
        
        // Generate pattern-based hint
        return this.generatePatternBasedHint(pattern, level, problem);
    }

    getProblemKey(problem) {
        return problem.slug || problem.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    getSpecificHint(problemKey, level, userContext) {
        const hints = this.hintDatabase[problemKey];
        let hint = hints[level];
        
        // Personalize hint based on user context
        if (userContext.previousAttempts > 0) {
            hint = this.addEncouragement(hint);
        }
        
        if (userContext.preferredLanguage) {
            hint = this.addLanguageSpecificTips(hint, userContext.preferredLanguage);
        }
        
        return hint;
    }

    generatePatternBasedHint(pattern, level, problem) {
        const patternHints = this.getPatternHints(pattern);
        const baseHint = patternHints[level] || patternHints.gentle;
        
        // Customize hint for specific problem context
        return this.customizeHintForProblem(baseHint, problem, level);
    }

    getPatternHints(pattern) {
        const patternHintMap = {
            'two-pointers': {
                gentle: 'Consider using two pointers moving in opposite directions or at different speeds.',
                moderate: 'Initialize two pointers and move them based on certain conditions to find your answer.',
                strong: 'Use left and right pointers. Move them inward based on comparison results until they meet or cross.'
            },
            'sliding-window': {
                gentle: 'Think about maintaining a window of elements and sliding it across the data.',
                moderate: 'Use two pointers to maintain a window. Expand or shrink the window based on conditions.',
                strong: 'Maintain left and right pointers for window boundaries. Expand right to include elements, shrink left when condition is violated.'
            },
            'hash-map': {
                gentle: 'Consider what data structure allows fast lookups and storage of key-value pairs.',
                moderate: 'Use a hash map to store elements as you iterate, checking for required values.',
                strong: 'Create a hash map. For each element, check if the complement exists in the map, then add current element.'
            },
            'dynamic-programming': {
                gentle: 'Think about breaking this problem into smaller, overlapping subproblems.',
                moderate: 'Consider if you can build the solution using previously computed results.',
                strong: 'Define your DP state, write the recurrence relation, and implement either top-down or bottom-up approach.'
            },
            'binary-search': {
                gentle: 'The data seems to have some ordering. Can you eliminate half the search space each time?',
                moderate: 'Use binary search by comparing the middle element and deciding which half to search.',
                strong: 'Set left=0, right=length-1. While left<=right, check mid=(left+right)/2 and adjust boundaries.'
            },
            'tree-traversal': {
                gentle: 'Think about different ways to visit all nodes in a tree systematically.',
                moderate: 'Consider using recursion or a stack/queue to traverse the tree in the required order.',
                strong: 'Use DFS (preorder/inorder/postorder) or BFS depending on the problem requirements. Handle null nodes carefully.'
            },
            'graph-traversal': {
                gentle: 'Consider how to systematically visit all connected nodes in a graph.',
                moderate: 'Use DFS or BFS to explore the graph, keeping track of visited nodes.',
                strong: 'Implement DFS with recursion/stack or BFS with queue. Use a visited set to avoid cycles.'
            },
            'backtracking': {
                gentle: 'Think about exploring all possibilities and undoing choices that don\'t lead to a solution.',
                moderate: 'Build the solution incrementally, backtrack when you hit a dead end.',
                strong: 'Use recursion to try each possibility. Add to current solution, recurse, then remove (backtrack).'
            },
            'greedy': {
                gentle: 'Consider making the locally optimal choice at each step.',
                moderate: 'Sort the data if needed, then make greedy choices based on the problem constraints.',
                strong: 'Prove that greedy choice leads to optimal solution, then implement the greedy strategy.'
            },
            'divide-and-conquer': {
                gentle: 'Can you break this problem into smaller, similar subproblems?',
                moderate: 'Divide the problem in half, solve each half recursively, then combine the results.',
                strong: 'Implement divide (split problem), conquer (solve subproblems), and combine (merge results) steps.'
            }
        };

        return patternHintMap[pattern] || patternHintMap['hash-map'];
    }

    customizeHintForProblem(baseHint, problem, level) {
        let customizedHint = baseHint;
        
        // Add problem-specific context
        if (problem.difficulty === 'Easy' && level === 'strong') {
            customizedHint += ' This is an easy problem, so the solution should be straightforward.';
        } else if (problem.difficulty === 'Hard' && level === 'gentle') {
            customizedHint += ' This is a hard problem, so take your time to understand the requirements first.';
        }
        
        // Add category-specific tips
        if (problem.category) {
            const categoryTips = this.getCategoryTips(problem.category, level);
            if (categoryTips) {
                customizedHint += ` ${categoryTips}`;
            }
        }
        
        return customizedHint;
    }

    getCategoryTips(category, level) {
        const categoryTipMap = {
            'Array': {
                gentle: 'Pay attention to array indices and bounds.',
                moderate: 'Consider sorting or using extra space for optimization.',
                strong: 'Think about in-place operations or two-pass algorithms.'
            },
            'String': {
                gentle: 'Consider character-by-character processing.',
                moderate: 'Think about string manipulation techniques like sliding window.',
                strong: 'Consider using character arrays or StringBuilder for efficiency.'
            },
            'Linked List': {
                gentle: 'Remember to handle null pointers carefully.',
                moderate: 'Consider using dummy nodes to simplify edge cases.',
                strong: 'Think about two-pointer techniques for linked lists.'
            },
            'Tree': {
                gentle: 'Consider recursive solutions for tree problems.',
                moderate: 'Think about tree traversal patterns (DFS/BFS).',
                strong: 'Consider iterative solutions using stacks or queues.'
            },
            'Graph': {
                gentle: 'Think about how nodes are connected.',
                moderate: 'Consider using adjacency lists or matrices.',
                strong: 'Implement proper graph traversal with cycle detection.'
            }
        };

        return categoryTipMap[category]?.[level];
    }

    addEncouragement(hint) {
        const encouragements = [
            "Don't give up! ",
            "You're on the right track. ",
            "Keep thinking! ",
            "You can do this! "
        ];
        
        const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
        return randomEncouragement + hint;
    }

    addLanguageSpecificTips(hint, language) {
        const languageTips = {
            'javascript': ' In JavaScript, consider using Map() for hash maps and array methods like filter(), map().',
            'python': ' In Python, use dict() for hash maps and list comprehensions for concise code.',
            'java': ' In Java, use HashMap<> and ArrayList<> for dynamic data structures.',
            'cpp': ' In C++, use unordered_map and vector for efficient operations.',
            'csharp': ' In C#, use Dictionary<> and List<> for collections.'
        };

        return hint + (languageTips[language.toLowerCase()] || '');
    }

    trackHintUsage(problemKey, level, wasHelpful) {
        if (!this.hintHistory.has(problemKey)) {
            this.hintHistory.set(problemKey, {});
        }
        
        const problemHints = this.hintHistory.get(problemKey);
        if (!problemHints[level]) {
            problemHints[level] = { used: 0, helpful: 0 };
        }
        
        problemHints[level].used++;
        if (wasHelpful) {
            problemHints[level].helpful++;
        }
    }

    getHintEffectiveness(problemKey, level) {
        const problemHints = this.hintHistory.get(problemKey);
        if (!problemHints || !problemHints[level]) {
            return 0;
        }
        
        const stats = problemHints[level];
        return stats.used > 0 ? stats.helpful / stats.used : 0;
    }

    initializeHintDatabase() {
        return {
            'two-sum': {
                gentle: 'Think about what happens when you need to find two numbers that add up to a target. What data structure allows fast lookups?',
                moderate: 'Use a hash map to store numbers you\'ve seen along with their indices. For each new number, check if its complement exists.',
                strong: 'Iterate through the array once. For each number, calculate complement = target - number. Check if complement exists in hash map. If yes, return [map[complement], currentIndex]. Otherwise, add current number and index to map.'
            },
            'add-two-numbers': {
                gentle: 'Think about how you would add two numbers digit by digit, just like you learned in elementary school.',
                moderate: 'Process both linked lists simultaneously, adding corresponding digits plus any carry from the previous addition.',
                strong: 'Create a dummy head for the result. Use a pointer to build the result list. While either list has nodes or there\'s a carry: sum the values, create new node with sum%10, update carry = sum/10, advance pointers.'
            },
            'longest-substring-without-repeating-characters': {
                gentle: 'Consider keeping track of characters you\'ve seen in the current substring. What happens when you encounter a duplicate?',
                moderate: 'Use a sliding window approach with a set to track unique characters. When you find a duplicate, shrink the window from the left.',
                strong: 'Use two pointers (left, right) and a Set. Expand right pointer and add characters to set. When duplicate found, remove characters from left until duplicate is gone. Track maximum window size.'
            },
            'valid-parentheses': {
                gentle: 'Think about the Last In, First Out (LIFO) nature of matching parentheses.',
                moderate: 'Use a stack to keep track of opening brackets. When you see a closing bracket, check if it matches the most recent opening bracket.',
                strong: 'Use a stack and a map for bracket pairs. For each character: if opening bracket, push to stack; if closing bracket, check if stack is empty or top doesn\'t match - return false; finally, return stack.isEmpty().'
            },
            'merge-two-sorted-lists': {
                gentle: 'Since both lists are already sorted, you can compare elements from the front of each list.',
                moderate: 'Use two pointers, one for each list. Compare current nodes and add the smaller one to your result.',
                strong: 'Create dummy head. Use pointer to build result. While both lists have nodes: compare values, add smaller node to result, advance that list\'s pointer. After loop, append remaining nodes from non-empty list.'
            }
        };
    }
}

// Problem Pattern Recognition System
class ProblemPatternRecognizer {
    identifyPattern(problem) {
        const title = problem.title.toLowerCase();
        const description = (problem.description || '').toLowerCase();
        const text = title + ' ' + description;

        // Pattern recognition rules
        const patterns = [
            { pattern: 'two-pointers', keywords: ['two pointer', 'left right', 'start end', 'palindrome', 'sorted array'] },
            { pattern: 'sliding-window', keywords: ['substring', 'subarray', 'window', 'consecutive', 'contiguous'] },
            { pattern: 'hash-map', keywords: ['frequency', 'count', 'duplicate', 'unique', 'anagram', 'sum'] },
            { pattern: 'dynamic-programming', keywords: ['optimal', 'maximum', 'minimum', 'ways', 'fibonacci', 'climb'] },
            { pattern: 'binary-search', keywords: ['sorted', 'search', 'find', 'log', 'binary'] },
            { pattern: 'tree-traversal', keywords: ['tree', 'binary tree', 'root', 'leaf', 'node', 'traversal'] },
            { pattern: 'graph-traversal', keywords: ['graph', 'connected', 'path', 'cycle', 'island', 'network'] },
            { pattern: 'backtracking', keywords: ['permutation', 'combination', 'generate', 'all possible', 'backtrack'] },
            { pattern: 'greedy', keywords: ['greedy', 'interval', 'schedule', 'activity', 'minimum', 'maximum'] },
            { pattern: 'divide-and-conquer', keywords: ['divide', 'merge', 'sort', 'quick', 'binary'] }
        ];

        // Find the best matching pattern
        let bestMatch = { pattern: 'hash-map', score: 0 };
        
        for (const patternInfo of patterns) {
            let score = 0;
            for (const keyword of patternInfo.keywords) {
                if (text.includes(keyword)) {
                    score += keyword.length; // Longer keywords get higher weight
                }
            }
            
            if (score > bestMatch.score) {
                bestMatch = { pattern: patternInfo.pattern, score };
            }
        }

        return bestMatch.pattern;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AdvancedHintSystem, ProblemPatternRecognizer };
}
