"""
Advanced Difficulty Detection Service for LeetCode Helper Pro
Detects when users are struggling and suggests appropriate interventions
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.models.schemas import (
    ProblemData, UserContext, StruggleDetectionRequest, StruggleDetectionResponse
)

logger = logging.getLogger(__name__)


class StruggleLevel(Enum):
    """Different levels of user struggle"""
    NONE = "none"
    MILD = "mild"
    MODERATE = "moderate"
    SEVERE = "severe"


@dataclass
class StruggleIndicator:
    """Individual struggle indicator with weight and description"""
    name: str
    weight: float
    value: float
    description: str
    triggered: bool = False


class DifficultyDetector:
    """Advanced system for detecting user difficulty and struggle patterns"""
    
    def __init__(self):
        self.struggle_thresholds = {
            StruggleLevel.MILD: 0.3,
            StruggleLevel.MODERATE: 0.6,
            StruggleLevel.SEVERE: 0.8
        }
        
        # Weights for different struggle indicators
        self.indicator_weights = {
            'hint_frequency': 0.25,
            'time_spent': 0.20,
            'interaction_pattern': 0.15,
            'problem_difficulty': 0.15,
            'user_behavior': 0.10,
            'session_context': 0.10,
            'error_patterns': 0.05
        }
    
    async def detect_struggle(self, request: StruggleDetectionRequest) -> StruggleDetectionResponse:
        """Main method to detect user struggle and recommend interventions"""
        
        try:
            # Analyze different struggle indicators
            indicators = await self._analyze_struggle_indicators(request)
            
            # Calculate overall struggle score
            struggle_score = self._calculate_struggle_score(indicators)
            
            # Determine struggle level
            struggle_level = self._determine_struggle_level(struggle_score)
            
            # Generate recommendations based on struggle level
            recommendations = self._generate_recommendations(struggle_level, indicators, request)
            
            # Determine if YouTube should be suggested
            suggest_youtube = self._should_suggest_youtube(struggle_level, indicators, request)
            
            # Create response
            response = StruggleDetectionResponse(
                is_struggling=struggle_level != StruggleLevel.NONE,
                struggle_score=min(struggle_score, 1.0),
                indicators=[ind.description for ind in indicators if ind.triggered],
                recommendations=recommendations,
                suggest_youtube=suggest_youtube
            )
            
            logger.info(f"Struggle detection for {request.problem_data.title}: "
                       f"level={struggle_level.value}, score={struggle_score:.2f}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error in struggle detection: {e}")
            return self._create_fallback_response()
    
    async def _analyze_struggle_indicators(self, request: StruggleDetectionRequest) -> List[StruggleIndicator]:
        """Analyze various indicators of user struggle"""
        
        indicators = []
        
        # 1. Hint frequency analysis
        hint_indicator = self._analyze_hint_frequency(request.user_context)
        indicators.append(hint_indicator)
        
        # 2. Time spent analysis
        time_indicator = self._analyze_time_spent(request.user_context, request.problem_data)
        indicators.append(time_indicator)
        
        # 3. Interaction pattern analysis
        interaction_indicator = self._analyze_interaction_patterns(request)
        indicators.append(interaction_indicator)
        
        # 4. Problem difficulty vs user behavior
        difficulty_indicator = self._analyze_problem_difficulty_mismatch(request)
        indicators.append(difficulty_indicator)
        
        # 5. User behavior patterns
        behavior_indicator = self._analyze_user_behavior(request.user_context)
        indicators.append(behavior_indicator)
        
        # 6. Session context analysis
        session_indicator = self._analyze_session_context(request)
        indicators.append(session_indicator)
        
        # 7. Error pattern analysis (if available)
        error_indicator = self._analyze_error_patterns(request)
        indicators.append(error_indicator)
        
        return indicators
    
    def _analyze_hint_frequency(self, user_context: UserContext) -> StruggleIndicator:
        """Analyze hint request frequency"""
        
        hint_count = user_context.hint_count or 0
        
        # Calculate struggle value based on hint count
        if hint_count == 0:
            value = 0.0
            description = "No hints requested yet"
        elif hint_count <= 2:
            value = 0.2
            description = f"Requested {hint_count} hint(s) - normal exploration"
        elif hint_count <= 4:
            value = 0.5
            description = f"Requested {hint_count} hints - showing some difficulty"
        else:
            value = 0.8
            description = f"Requested {hint_count} hints - significant struggle"
        
        return StruggleIndicator(
            name="hint_frequency",
            weight=self.indicator_weights['hint_frequency'],
            value=value,
            description=description,
            triggered=value > 0.3
        )
    
    def _analyze_time_spent(self, user_context: UserContext, problem_data: ProblemData) -> StruggleIndicator:
        """Analyze time spent on problem relative to difficulty"""
        
        time_spent = user_context.time_spent or 0
        difficulty = problem_data.difficulty
        
        # Expected time ranges by difficulty (in minutes)
        expected_times = {
            "Easy": (5, 15),
            "Medium": (15, 30),
            "Hard": (30, 60)
        }
        
        min_time, max_time = expected_times.get(difficulty, (15, 30))
        
        if time_spent < min_time:
            value = 0.0
            description = f"Time spent ({time_spent}m) is within normal range"
        elif time_spent <= max_time:
            value = 0.3
            description = f"Time spent ({time_spent}m) is reasonable for {difficulty} problem"
        elif time_spent <= max_time * 1.5:
            value = 0.6
            description = f"Time spent ({time_spent}m) is above average for {difficulty} problem"
        else:
            value = 0.9
            description = f"Time spent ({time_spent}m) is significantly high for {difficulty} problem"
        
        return StruggleIndicator(
            name="time_spent",
            weight=self.indicator_weights['time_spent'],
            value=value,
            description=description,
            triggered=value > 0.4
        )
    
    def _analyze_interaction_patterns(self, request: StruggleDetectionRequest) -> StruggleIndicator:
        """Analyze user interaction patterns"""
        
        interaction_history = request.interaction_history or []
        
        if not interaction_history:
            return StruggleIndicator(
                name="interaction_pattern",
                weight=self.indicator_weights['interaction_pattern'],
                value=0.0,
                description="No interaction history available",
                triggered=False
            )
        
        # Analyze recent interactions (last 10 minutes)
        recent_cutoff = datetime.utcnow() - timedelta(minutes=10)
        recent_interactions = [
            interaction for interaction in interaction_history
            if datetime.fromisoformat(interaction.get('timestamp', '2000-01-01')) > recent_cutoff
        ]
        
        interaction_count = len(recent_interactions)
        
        if interaction_count <= 2:
            value = 0.1
            description = "Normal interaction frequency"
        elif interaction_count <= 5:
            value = 0.4
            description = "Moderate interaction frequency - exploring options"
        elif interaction_count <= 8:
            value = 0.7
            description = "High interaction frequency - possible confusion"
        else:
            value = 0.9
            description = "Very high interaction frequency - likely struggling"
        
        return StruggleIndicator(
            name="interaction_pattern",
            weight=self.indicator_weights['interaction_pattern'],
            value=value,
            description=description,
            triggered=value > 0.5
        )
    
    def _analyze_problem_difficulty_mismatch(self, request: StruggleDetectionRequest) -> StruggleIndicator:
        """Analyze mismatch between problem difficulty and user behavior"""
        
        difficulty = request.problem_data.difficulty
        hint_count = request.user_context.hint_count or 0
        time_spent = request.user_context.time_spent or 0
        
        # Define expected behavior for each difficulty
        difficulty_expectations = {
            "Easy": {"max_hints": 1, "max_time": 15},
            "Medium": {"max_hints": 2, "max_time": 25},
            "Hard": {"max_hints": 3, "max_time": 45}
        }
        
        expectations = difficulty_expectations.get(difficulty, {"max_hints": 2, "max_time": 25})
        
        hint_excess = max(0, hint_count - expectations["max_hints"])
        time_excess = max(0, time_spent - expectations["max_time"])
        
        # Calculate mismatch score
        hint_score = min(hint_excess / 3, 1.0)  # Normalize to 0-1
        time_score = min(time_excess / expectations["max_time"], 1.0)  # Normalize to 0-1
        
        value = (hint_score + time_score) / 2
        
        if value < 0.2:
            description = f"Behavior matches {difficulty} problem expectations"
        elif value < 0.5:
            description = f"Slightly above expected difficulty for {difficulty} problem"
        elif value < 0.8:
            description = f"Significantly above expected difficulty for {difficulty} problem"
        else:
            description = f"Behavior suggests major difficulty with {difficulty} problem"
        
        return StruggleIndicator(
            name="problem_difficulty",
            weight=self.indicator_weights['problem_difficulty'],
            value=value,
            description=description,
            triggered=value > 0.4
        )
    
    def _analyze_user_behavior(self, user_context: UserContext) -> StruggleIndicator:
        """Analyze general user behavior patterns"""
        
        struggle_indicators = user_context.struggle_indicators or []
        
        # Count different types of struggle indicators
        indicator_count = len(struggle_indicators)
        
        if indicator_count == 0:
            value = 0.0
            description = "No struggle indicators detected"
        elif indicator_count <= 2:
            value = 0.3
            description = f"Minor struggle indicators: {', '.join(struggle_indicators[:2])}"
        elif indicator_count <= 4:
            value = 0.6
            description = f"Multiple struggle indicators: {', '.join(struggle_indicators[:3])}"
        else:
            value = 0.8
            description = f"Many struggle indicators: {', '.join(struggle_indicators[:3])} and {indicator_count-3} more"
        
        return StruggleIndicator(
            name="user_behavior",
            weight=self.indicator_weights['user_behavior'],
            value=value,
            description=description,
            triggered=value > 0.4
        )
    
    def _analyze_session_context(self, request: StruggleDetectionRequest) -> StruggleIndicator:
        """Analyze session-level context"""
        
        previous_hints = request.user_context.previous_hints or []
        
        # Analyze hint progression
        if not previous_hints:
            value = 0.0
            description = "First interaction in session"
        elif len(previous_hints) <= 2:
            value = 0.2
            description = "Early in problem-solving session"
        elif len(previous_hints) <= 4:
            value = 0.5
            description = "Extended problem-solving session"
        else:
            value = 0.7
            description = "Very long problem-solving session"
        
        return StruggleIndicator(
            name="session_context",
            weight=self.indicator_weights['session_context'],
            value=value,
            description=description,
            triggered=value > 0.4
        )
    
    def _analyze_error_patterns(self, request: StruggleDetectionRequest) -> StruggleIndicator:
        """Analyze error patterns if available"""
        
        # This would analyze coding errors, failed test cases, etc.
        # For now, return a neutral indicator
        return StruggleIndicator(
            name="error_patterns",
            weight=self.indicator_weights['error_patterns'],
            value=0.0,
            description="Error pattern analysis not available",
            triggered=False
        )
    
    def _calculate_struggle_score(self, indicators: List[StruggleIndicator]) -> float:
        """Calculate weighted struggle score from indicators"""
        
        total_score = 0.0
        total_weight = 0.0
        
        for indicator in indicators:
            weighted_score = indicator.value * indicator.weight
            total_score += weighted_score
            total_weight += indicator.weight
        
        # Normalize by total weight
        if total_weight > 0:
            return total_score / total_weight
        else:
            return 0.0
    
    def _determine_struggle_level(self, struggle_score: float) -> StruggleLevel:
        """Determine struggle level from score"""
        
        if struggle_score >= self.struggle_thresholds[StruggleLevel.SEVERE]:
            return StruggleLevel.SEVERE
        elif struggle_score >= self.struggle_thresholds[StruggleLevel.MODERATE]:
            return StruggleLevel.MODERATE
        elif struggle_score >= self.struggle_thresholds[StruggleLevel.MILD]:
            return StruggleLevel.MILD
        else:
            return StruggleLevel.NONE
    
    def _generate_recommendations(self, struggle_level: StruggleLevel, 
                                indicators: List[StruggleIndicator], 
                                request: StruggleDetectionRequest) -> List[str]:
        """Generate recommendations based on struggle level and indicators"""
        
        recommendations = []
        
        if struggle_level == StruggleLevel.NONE:
            recommendations.append("You're doing well! Keep up the good work.")
        
        elif struggle_level == StruggleLevel.MILD:
            recommendations.extend([
                "Take a step back and re-read the problem carefully",
                "Try working through a simple example manually",
                "Consider what data structures might be helpful"
            ])
        
        elif struggle_level == StruggleLevel.MODERATE:
            recommendations.extend([
                "Break the problem down into smaller subproblems",
                "Consider looking up the problem category for common patterns",
                "Try explaining the problem to yourself out loud",
                "Draw diagrams or write pseudocode first"
            ])
        
        elif struggle_level == StruggleLevel.SEVERE:
            recommendations.extend([
                "Consider taking a short break to clear your mind",
                "Review fundamental concepts related to this problem type",
                "Watch solution videos to understand the approach",
                "Practice similar easier problems first"
            ])
        
        # Add specific recommendations based on triggered indicators
        for indicator in indicators:
            if indicator.triggered:
                if indicator.name == "hint_frequency":
                    recommendations.append("Try to work through hints systematically rather than requesting more")
                elif indicator.name == "time_spent":
                    recommendations.append("Consider if you're overthinking the problem")
                elif indicator.name == "interaction_pattern":
                    recommendations.append("Focus on one approach at a time")
        
        return recommendations[:5]  # Limit to 5 recommendations
    
    def _should_suggest_youtube(self, struggle_level: StruggleLevel, 
                              indicators: List[StruggleIndicator], 
                              request: StruggleDetectionRequest) -> bool:
        """Determine if YouTube videos should be suggested"""
        
        # Suggest YouTube for moderate to severe struggle
        if struggle_level in [StruggleLevel.MODERATE, StruggleLevel.SEVERE]:
            return True
        
        # Also suggest if specific indicators are strongly triggered
        for indicator in indicators:
            if indicator.triggered and indicator.value > 0.7:
                if indicator.name in ["hint_frequency", "time_spent", "problem_difficulty"]:
                    return True
        
        return False
    
    def _create_fallback_response(self) -> StruggleDetectionResponse:
        """Create fallback response when detection fails"""
        
        return StruggleDetectionResponse(
            is_struggling=False,
            struggle_score=0.0,
            indicators=["Detection system temporarily unavailable"],
            recommendations=["Continue working on the problem step by step"],
            suggest_youtube=False
        )
