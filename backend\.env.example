# LeetCode Helper Pro AI Backend Configuration

# Basic Settings
DEBUG=true
HOST=localhost
PORT=8000

# CORS Settings (add your extension ID)
ALLOWED_ORIGINS=["http://localhost:3000", "chrome-extension://your-extension-id"]

# AI API Keys
GOOGLE_API_KEY=AIzaSyAPNOL93b6xIrMMevhOweeRvv_rLC-EFzs
# YouTube API
YOUTUBE_API_KEY=AIzaSyDW6kQI2sI2QPtHZ5NaiWUr_EPT-Ud4wVk
# Database
DATABASE_URL=sqlite:///./leetcode_helper.db

# Redis (optional)
REDIS_URL=redis://localhost:6379
REDIS_ENABLED=false

# AI Model Settings
DEFAULT_AI_MODEL=gemini-pro
MAX_TOKENS=1000
TEMPERATURE=0.7

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Security
SECRET_KEY=your-secret-key-change-in-production

# Feature Flags
ENABLE_YOUTUBE_SEARCH=true
ENABLE_AI_HINTS=true
ENABLE_PROBLEM_ANALYSIS=true

# Cache Settings
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# AI Prompts (optional customization)
HINT_SYSTEM_PROMPT="You are an expert programming tutor helping students learn to solve coding problems. Your goal is to guide students toward the solution without giving direct code. Provide hints that encourage thinking and learning."

ANALYSIS_SYSTEM_PROMPT="You are an expert algorithm analyst. Analyze the given coding problem and provide insights about the optimal approach, time/space complexity, and key concepts involved."
