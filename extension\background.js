// Background Service Worker for LeetCode Helper Pro
class LeetCodeHelperBackground {
    constructor() {
        this.currentProblem = null;
        this.hintSystem = null;
        this.approachAnalyzer = null;
        this.testCaseGenerator = null;
        this.languageSupport = null;
        this.progressTracker = null;
        this.mockDataProvider = null;
        this.userProgress = {
            problemsSolved: 0,
            hintsUsed: 0,
            badges: [],
            preferences: {
                autoDetect: true,
                showContextMenu: true,
                preferredLanguage: 'javascript',
                hintPreference: 'progressive'
            }
        };
        this.init();
    }

    async init() {
        // Initialize systems
        await this.initializeSystems();

        this.setupMessageHandlers();
        this.setupContextMenu();
        this.loadUserData();
        this.setupInstallHandler();
    }

    async initializeSystems() {
        try {
            // Import and initialize all systems
            this.mockDataProvider = new MockDataProvider();

            // Initialize other systems (would normally import from separate files)
            this.hintSystem = {
                generateHint: async (problem, level) => {
                    return this.mockDataProvider.getMockHints(problem.slug, level);
                }
            };

            this.approachAnalyzer = {
                analyzeApproach: async (problem) => {
                    return this.mockDataProvider.getMockApproach(problem.slug);
                }
            };

            this.testCaseGenerator = {
                generateTestCases: async (problem) => {
                    return this.mockDataProvider.getMockTestCases(problem.slug);
                }
            };

            this.languageSupport = {
                detectCurrentLanguage: () => 'javascript',
                getLanguageInfo: (lang) => ({ name: lang, tips: [] })
            };

            this.progressTracker = {
                trackHintUsage: (level, problem) => {
                    this.userProgress.hintsUsed++;
                    this.saveUserData();
                },
                getProgressSummary: () => this.mockDataProvider.getMockUserProgress()
            };

        } catch (error) {
            console.error('Error initializing systems:', error);
        }
    }

    setupMessageHandlers() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.type) {
                case 'PROBLEM_DETECTED':
                    this.handleProblemDetected(message.problem);
                    break;
                
                case 'GENERATE_HINT':
                    this.generateHint(message.problem, message.level)
                        .then(hint => sendResponse({ hint }));
                    return true; // Keep message channel open for async response
                
                case 'GET_USER_PROGRESS':
                    sendResponse({ progress: this.userProgress });
                    break;
                
                case 'UPDATE_PROGRESS':
                    this.updateProgress(message.data);
                    break;
                
                case 'GET_TEST_CASES':
                    this.generateTestCases(message.problem)
                        .then(testCases => sendResponse({ testCases }));
                    return true;
                
                case 'ANALYZE_APPROACH':
                    this.analyzeApproach(message.problem)
                        .then(analysis => sendResponse({ analysis }));
                    return true;
            }
        });
    }

    setupContextMenu() {
        chrome.contextMenus.removeAll(() => {
            // Main menu item
            chrome.contextMenus.create({
                id: 'leetcode-helper-main',
                title: 'LeetCode Helper Pro',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            // Hint submenu
            chrome.contextMenus.create({
                id: 'leetcode-helper-gentle-hint',
                parentId: 'leetcode-helper-main',
                title: '💡 Gentle Hint',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            chrome.contextMenus.create({
                id: 'leetcode-helper-moderate-hint',
                parentId: 'leetcode-helper-main',
                title: '🔍 Moderate Hint',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            chrome.contextMenus.create({
                id: 'leetcode-helper-strong-hint',
                parentId: 'leetcode-helper-main',
                title: '🎯 Strong Hint',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            // Separator
            chrome.contextMenus.create({
                id: 'separator1',
                parentId: 'leetcode-helper-main',
                type: 'separator',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            // Analysis tools
            chrome.contextMenus.create({
                id: 'leetcode-helper-approach',
                parentId: 'leetcode-helper-main',
                title: '📊 Analyze Approach',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            chrome.contextMenus.create({
                id: 'leetcode-helper-test-cases',
                parentId: 'leetcode-helper-main',
                title: '🧪 Generate Test Cases',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            // Separator
            chrome.contextMenus.create({
                id: 'separator2',
                parentId: 'leetcode-helper-main',
                type: 'separator',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });

            // Quick actions
            chrome.contextMenus.create({
                id: 'leetcode-helper-hint',
                parentId: 'leetcode-helper-main',
                title: '⚡ Quick Hint Panel',
                contexts: ['page'],
                documentUrlPatterns: ['*://leetcode.com/problems/*', '*://leetcode.cn/problems/*']
            });
        });

        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });
    }

    setupInstallHandler() {
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.showWelcomeNotification();
                this.initializeUserData();
            } else if (details.reason === 'update') {
                this.handleUpdate(details.previousVersion);
            }
        });
    }

    handleProblemDetected(problem) {
        this.currentProblem = problem;
        this.updateBadge(problem);
        this.trackProblemVisit(problem);
    }

    async generateHint(problem, level) {
        if (!problem) return 'Please navigate to a LeetCode problem first.';

        // Track hint usage
        if (this.progressTracker) {
            this.progressTracker.trackHintUsage(level, problem);
        }

        // Generate hint using hint system
        if (this.hintSystem) {
            return await this.hintSystem.generateHint(problem, level);
        }

        // Fallback to mock data
        return this.mockDataProvider ?
            this.mockDataProvider.getMockHints(problem.slug, level) :
            'Hint system not available';
    }

    async generateTestCases(problem) {
        if (!problem) return [];

        if (this.testCaseGenerator) {
            return await this.testCaseGenerator.generateTestCases(problem);
        }

        return this.mockDataProvider ?
            this.mockDataProvider.getMockTestCases(problem.slug) :
            [];
    }

    async analyzeApproach(problem) {
        if (!problem) return null;

        if (this.approachAnalyzer) {
            return await this.approachAnalyzer.analyzeApproach(problem);
        }

        return this.mockDataProvider ?
            this.mockDataProvider.getMockApproach(problem.slug) :
            null;
    }

    handleContextMenuClick(info, tab) {
        switch (info.menuItemId) {
            case 'leetcode-helper-hint':
                this.showQuickHint(tab);
                break;
            case 'leetcode-helper-approach':
                this.showApproachAnalysis(tab);
                break;
            case 'leetcode-helper-test-cases':
                this.showTestCases(tab);
                break;
            case 'leetcode-helper-gentle-hint':
                this.showSpecificHint(tab, 'gentle');
                break;
            case 'leetcode-helper-moderate-hint':
                this.showSpecificHint(tab, 'moderate');
                break;
            case 'leetcode-helper-strong-hint':
                this.showSpecificHint(tab, 'strong');
                break;
        }
    }

    showQuickHint(tab) {
        chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_QUICK_HINT',
            problem: this.currentProblem
        });
    }

    showSpecificHint(tab, level) {
        chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_SPECIFIC_HINT',
            problem: this.currentProblem,
            level: level
        });
    }

    showApproachAnalysis(tab) {
        chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_APPROACH_ANALYSIS',
            problem: this.currentProblem
        });
    }

    showTestCases(tab) {
        chrome.tabs.sendMessage(tab.id, {
            type: 'SHOW_TEST_CASES',
            problem: this.currentProblem
        });
    }

    updateBadge(problem) {
        if (problem && problem.title) {
            chrome.action.setBadgeText({ text: '✓' });
            chrome.action.setBadgeBackgroundColor({ color: '#4CAF50' });
            chrome.action.setTitle({ title: `LeetCode Helper - ${problem.title}` });
        } else {
            chrome.action.setBadgeText({ text: '' });
            chrome.action.setTitle({ title: 'LeetCode Helper Pro' });
        }
    }

    trackProblemVisit(problem) {
        // Track problem visits for analytics
        const visitData = {
            problem: problem.slug,
            timestamp: Date.now(),
            difficulty: problem.difficulty
        };

        chrome.storage.local.get(['problemVisits'], (result) => {
            const visits = result.problemVisits || [];
            visits.push(visitData);
            
            // Keep only last 100 visits
            if (visits.length > 100) {
                visits.splice(0, visits.length - 100);
            }
            
            chrome.storage.local.set({ problemVisits: visits });
        });
    }

    updateProgress(data) {
        Object.assign(this.userProgress, data);
        this.saveUserData();
        this.checkForBadges();
    }

    checkForBadges() {
        const badges = [];
        
        if (this.userProgress.problemsSolved >= 1 && !this.userProgress.badges.includes('first-solve')) {
            badges.push('first-solve');
        }
        
        if (this.userProgress.problemsSolved >= 10 && !this.userProgress.badges.includes('problem-solver')) {
            badges.push('problem-solver');
        }
        
        if (this.userProgress.hintsUsed >= 5 && !this.userProgress.badges.includes('hint-master')) {
            badges.push('hint-master');
        }

        badges.forEach(badge => {
            if (!this.userProgress.badges.includes(badge)) {
                this.userProgress.badges.push(badge);
                this.showBadgeNotification(badge);
            }
        });

        if (badges.length > 0) {
            this.saveUserData();
        }
    }

    showBadgeNotification(badge) {
        const badgeNames = {
            'first-solve': 'First Solve!',
            'problem-solver': 'Problem Solver',
            'hint-master': 'Hint Master'
        };

        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'New Badge Earned!',
            message: `You've earned the "${badgeNames[badge]}" badge!`
        });
    }

    showWelcomeNotification() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Welcome to LeetCode Helper Pro!',
            message: 'Your intelligent coding companion is ready to help you solve problems.'
        });
    }

    loadUserData() {
        chrome.storage.sync.get(['userProgress'], (result) => {
            if (result.userProgress) {
                this.userProgress = { ...this.userProgress, ...result.userProgress };
            }
        });
    }

    saveUserData() {
        chrome.storage.sync.set({ userProgress: this.userProgress });
    }

    initializeUserData() {
        this.saveUserData();
    }

    handleUpdate(previousVersion) {
        // Handle extension updates
        console.log(`Updated from version ${previousVersion}`);
    }
}

// Hint Generation System
class HintGenerator {
    async generateHint(problem, level) {
        const hintDatabase = await this.loadHintDatabase();
        const problemKey = problem.slug || problem.title.toLowerCase().replace(/\s+/g, '-');
        
        if (hintDatabase[problemKey] && hintDatabase[problemKey][level]) {
            return hintDatabase[problemKey][level];
        }
        
        // Fallback to generated hints
        return this.generateFallbackHint(problem, level);
    }

    async loadHintDatabase() {
        // In a real implementation, this would load from a comprehensive database
        return {
            'two-sum': {
                gentle: 'Think about what data structure allows fast lookups of previously seen values.',
                moderate: 'Use a hash map to store numbers and their indices as you iterate.',
                strong: 'For each number, check if (target - number) exists in your hash map. If yes, return indices.'
            },
            'add-two-numbers': {
                gentle: 'Consider how you would add two numbers digit by digit on paper.',
                moderate: 'Process both linked lists simultaneously, keeping track of carry values.',
                strong: 'Create a new linked list, add corresponding digits plus carry, handle different list lengths.'
            },
            'longest-substring-without-repeating-characters': {
                gentle: 'What if you kept track of characters you\'ve already seen in the current substring?',
                moderate: 'Use a sliding window approach with a set to track unique characters.',
                strong: 'Use two pointers and a set. Expand right pointer, shrink left when duplicate found.'
            }
        };
    }

    generateFallbackHint(problem, level) {
        const fallbacks = {
            gentle: 'Consider the problem constraints and think about what data structures might be helpful.',
            moderate: 'Break this problem down into smaller steps and consider the time complexity.',
            strong: 'Think about the most efficient algorithm for this type of problem and implement it step by step.'
        };
        
        return fallbacks[level] || fallbacks.gentle;
    }
}

// Test Case Generation System
class TestCaseGenerator {
    async generateTestCases(problem) {
        // Generate test cases based on problem type and constraints
        return [
            {
                input: 'Basic case',
                output: 'Expected output',
                explanation: 'Standard test case'
            },
            {
                input: 'Edge case',
                output: 'Edge output',
                explanation: 'Tests boundary conditions'
            },
            {
                input: 'Large input',
                output: 'Performance test',
                explanation: 'Tests algorithm efficiency'
            }
        ];
    }
}

// Approach Analysis System
class ApproachAnalyzer {
    async analyzeApproach(problem) {
        return {
            recommendedApproach: 'Hash Map / Two Pointer',
            timeComplexity: 'O(n)',
            spaceComplexity: 'O(n)',
            keyInsights: [
                'Trade space for time efficiency',
                'Avoid nested loops with smart data structures',
                'Consider edge cases and constraints'
            ],
            alternativeApproaches: [
                {
                    name: 'Brute Force',
                    timeComplexity: 'O(n²)',
                    spaceComplexity: 'O(1)',
                    pros: 'Simple to implement',
                    cons: 'Inefficient for large inputs'
                }
            ]
        };
    }
}

// Initialize background service
new LeetCodeHelperBackground();
